repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: check-added-large-files # prevents giant files from being committed.
        args: [ "--maxkb=29000" ]
      - id: check-merge-conflict # checks for files that contain merge conflict strings.
      - id: detect-private-key # some protection against committing secrets.
      - id: end-of-file-fixer # ensures that a file is either empty, or ends with one newline.
      - id: mixed-line-ending # replaces or checks mixed line ending.
      - id: no-commit-to-branch # don't commit to branch.
        args: [ "-b", "main", "-b", "release" ]
      - id: trailing-whitespace # trims trailing whitespace.

    # python specific checks
      - id: check-ast # simply checks whether the files parse as valid python.
        language_version: python3.11
      - id: debug-statements # checks for debugger imports and py37+ `breakpoint()` calls in python source.
        language_version: python3.11
      - id: requirements-txt-fixer # sorts entries in requirements.txt.

    # json specific checks
      - id: check-json # checks json files for parseable syntax.

    # yaml specific checks
      - id: check-yaml # checks yaml files for parseable syntax.

  - repo: https://github.com/python-poetry/poetry
    rev: 1.8.0
    hooks:
      - id: poetry-check
        args: ["--directory=pyproject.toml"]

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        name: isort (python)
        args: ["--settings-file=pyproject.toml"]

  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        args: ["--config=pyproject.toml"]
        language_version: python3.11
