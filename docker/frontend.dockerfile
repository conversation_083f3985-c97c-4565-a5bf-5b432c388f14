FROM public.ecr.aws/docker/library/node:20-slim AS builder

WORKDIR /app

ARG NINJA_STAGE_NAME

# Copy package files first for better layer caching
COPY /frontend/package*.json ./

# Install build dependencies for node-gyp
RUN apt-get update && apt-get install -y --no-install-recommends \
    python3 \
    make \
    g++ \
    build-essential \
    pkg-config \
    libcairo2-dev \
    libpango1.0-dev \
    libjpeg-dev \
    libgif-dev \
    librsvg2-dev \
    && rm -rf /var/lib/apt/lists/*

RUN npm install

# Copy the frontend code
COPY /frontend .

RUN npm run build

EXPOSE 80

ENV HOSTNAME=0.0.0.0
ENV PORT=80

# Default command is dev, but can be overridden in docker-compose
CMD ["npm", "start"]
