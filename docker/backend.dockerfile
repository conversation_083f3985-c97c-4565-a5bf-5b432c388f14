FROM --platform=linux/amd64 public.ecr.aws/docker/library/python:3.11.5-slim AS builder

# install poetry
RUN pip install poetry && poetry config virtualenvs.in-project true

#  setup code dir
COPY pyproject.toml poetry.lock  /code/
WORKDIR /code

ARG NINJA_CODE_ARTIFACT_TOKEN
RUN poetry config http-basic.ninjapypi aws ${NINJA_CODE_ARTIFACT_TOKEN}
RUN poetry install --no-interaction --no-ansi --no-root --with dev

FROM --platform=linux/amd64 public.ecr.aws/docker/library/python:3.11.5-slim
COPY --from=builder /code/.venv /code/.venv
# Copy application code
COPY /backend/src /code/src

# This volume to support file upload
VOLUME ["/tmp"]


# Add the venvs executables to path  (python, unvicorn, etc)
ENV PATH="/code/.venv/bin:$PATH"

# Enable code in src to be imported from anywhere
ENV PYTHONPATH="/code/src:$PYTHONPATH"

# Expose the port the app runs on
EXPOSE 80

# Calculate optimal worker count based on 16 vCPUs
# Using (2*CPU)+1 formula for CPU-bound applications
ENV WORKERS=2
ENV THREADS=1
ENV WORKER_CONNECTIONS=2000

# Gunicorn configuration
CMD ["sh", "-c", "gunicorn api:app \
     --workers $WORKERS \
     --worker-class uvicorn.workers.UvicornWorker \
     --bind 0.0.0.0:80 \
     --timeout 600 \
     --graceful-timeout 300 \
     --keep-alive 250 \
     --max-requests 20000 \
     --max-requests-jitter 400 \
     --forwarded-allow-ips '*' \
     --worker-connections $WORKER_CONNECTIONS \
     --worker-tmp-dir /dev/shm \
     --preload \
     --log-level info \
     --access-logfile - \
     --error-logfile - \
     --capture-output \
     --enable-stdio-inheritance \
     --threads $THREADS"]
