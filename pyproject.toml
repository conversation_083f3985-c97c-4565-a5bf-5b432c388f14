[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"


[tool.poetry]
name = "ninja-suna-manus"
version = "1.0.111"
packages = [
    # FIX THIS
  { include = "agentpress", from = "backend/src" },
    {include = "infra"},
    { include = "lambdas", from = "infra" },
]
description = "open source generalist AI Agent"
authors = ["marko-kraemer <<EMAIL>>"]
readme = "README.md"
license = "MIT"
homepage = "https://www.suna.so/"
repository = "https://github.com/kortix-ai/suna"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
[[tool.poetry.source]]
name = "ninjapypi"
# "Ninja PyPi - cache of both internal and external packages"
url = "https://ninja-tech-ai-591753572006.d.codeartifact.us-west-2.amazonaws.com/pypi/ninja-py-pi/simple"
priority = "primary"

[[tool.poetry.source]]
name = "ninja-internal-packages"
# "Authoritative root of internal packages - used only for publishing internal packages
url = "https://ninja-tech-ai-591753572006.d.codeartifact.us-west-2.amazonaws.com/pypi/ninja-internal-packages"
priority = "explicit"

[tool.poetry.dependencies]
python = "^3.11"
ninja-common = "^4.0.31"
streamlit-quill = "0.0.3"
python-dotenv = "1.0.1"
click = "8.1.7"
questionary = "2.0.1"
requests = "^2.31.0"
packaging = "24.1"
setuptools = "75.3.0"
asyncio = "3.4.3"
altair = "4.2.2"
prisma = "0.15.0"
fastapi = "0.115.6"
uvicorn = "0.27.1"
python-multipart = "0.0.20"
redis = "5.2.1"
upstash-redis = "1.3.0"
supabase = "^2.15.0"
pyjwt = {extras = ["crypto"], version = "^2.10.1"}
cryptography = "^45.0.4"
cffi = "^1.17.0"
exa-py = "^1.9.1"
e2b-code-interpreter = "^1.2.0"
certifi = "2024.2.2"
python-ripgrep = "0.0.6"
daytona_sdk = "^0.20.0"
boto3 = "^1.34.0"
openai = "^1.72.0"
streamlit = "^1.44.1"
nest-asyncio = "^1.6.0"
vncdotool = "^1.2.0"
tavily-python = "^0.5.4"
pytesseract = "^0.3.13"
stripe = "^12.0.1"
gunicorn = "^23.0.0"
starlette-context = "^0.4.0"
pre-commit = "^4.2.0"
ninja-feedback-client = "^0.0.41"
pydantic-settings = "^2.9.1"
aioboto3 = "^14.3.0"

[tool.poetry.group.infra]
optional = true
[tool.poetry.group.infra.dependencies]
aws-cdk-lib = "^2.147.0"
ninja-common-infra = "^4.0.7"


[tool.poetry.group.dev]
optional = true
[tool.poetry.group.dev.dependencies]
pytest = "8.3.3"
pytest-asyncio = "0.24.0"
pre-commit = "^4.2.0"
typer = "^0.15.4"
black = "^25.1.0"
isort = "^6.0.1"
pytest-cov = "^6.1.1"
pytest-env = "^1.1.5"
pytest-mock = "^3.14.1"
litellm = "^1.66.2"
pycognito = "^2024.5.1"

[tool.poetry.scripts]
agentpress = "agentpress.cli:main"


#
#  Tooling config
#
[tool.black]
line-length = 100
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 100
filter_files = true
combine_as_imports = true


[tool.pytest.ini_options]
pythonpath = [
  "./tests", "./backend", "./backend/src"
]
asyncio_mode = "auto"
env = [
    "SUPER_AGENT_FRONTEND_URL=http://localhost:3000",
    "SERVICE_NAME=ninja-suna-manus"
]
