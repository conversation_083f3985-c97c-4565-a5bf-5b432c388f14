name: Deploy via Code Pipeline

on:
  push:
    branches: [main]
    paths-ignore:
      - 'pyproject.toml' # Changes only to pyproject.toml (e.g. version bumps) will not trigger

permissions:
  id-token: write   # This is required for GitHub OIDC Provider to work
  contents: read  # This is required for actions/checkout

jobs:
  start-pipeline-and-publish-client:
    uses: NinjaTech-AI/ninja-github-workflows/.github/workflows/ninja-python-deploy-via-pipeline.yml@main
    secrets: inherit
    with:
      publish-api-client: false
