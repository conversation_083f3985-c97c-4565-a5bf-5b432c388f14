name: Validate App Logic Change

on:
  pull_request:
    branches:
      - main

jobs:
  ninja-python-app-change-checks:
    uses: NinjaTech-AI/ninja-github-workflows/.github/workflows/ninja-python-app-change-checks.yml@main
    secrets: inherit
    with:
      pytest-args: |
        --ignore=tests/unit/infra
      pytest-cov-enabled: false

  ninja-nextjs-app:
    name: ninja-nextjs-app-change-checks
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./frontend
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Run linter
        run: npm run lint
