services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --save 60 1 --loglevel warning
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  backend:
    container_name: ninja-suna-manus-backend
    build:
      context: ./
      dockerfile: docker/backend.dockerfile
      args:
        - NINJA_CODE_ARTIFACT_TOKEN=${NINJA_CODE_ARTIFACT_TOKEN}
    ports:
      - "8000:80"
    env_file:
      - ./backend/.env
    volumes:
      - ./backend/.env:/app/.env:ro
      - ./backend/src:/code/src
    environment:
      - ENV_MODE=local
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_SSL=False
      - SUPER_AGENT_FRONTEND_URL=http://localhost:3000
      - SERVICE_NAME=ninja-suna-manus
      - FEEDBACK_SERVICE_BASE_URL=${FEEDBACK_SERVICE_BASE_URL:-https://feedback.beta.myninja.ai}
      - MODEL_TO_USE=${MODEL_TO_USE:-alias/openai/gpt-4.1}
      - PYTHONUNBUFFERED=1
      - WORKERS=${WORKERS:-1}
      - WORKER_CONNECTIONS=${WORKER_CONNECTIONS:-1000}
      - THREADS=${THREADS:-1}
    command: >
      gunicorn api:app
      --workers ${WORKERS:-1}
      --worker-class uvicorn.workers.UvicornWorker
      --bind 0.0.0.0:80
      --timeout 600
      --graceful-timeout 300
      --keep-alive 250
      --max-requests 2000
      --max-requests-jitter 400
      --forwarded-allow-ips '*'
      --worker-connections ${WORKER_CONNECTIONS:-1000}
      --worker-tmp-dir /dev/shm
      --preload
      --log-level info
      --access-logfile -
      --error-logfile -
      --capture-output
      --enable-stdio-inheritance
      --threads ${THREADS:-1}
      --reload
    depends_on:
      redis:
        condition: service_healthy

  frontend:
    build:
      context: ./
      dockerfile: docker/frontend.dockerfile
      args:
        - NINJA_CODE_ARTIFACT_TOKEN=${NINJA_CODE_ARTIFACT_TOKEN}
        - NINJA_STAGE_NAME=local
    ports:
      - "3000:80"
    env_file:
      - frontend/.env.local
    volumes:
      - ./frontend/.env.local:/app/.env.local:ro
    environment:
      - NODE_ENV=production
    depends_on:
      - backend

volumes:
  redis-data:
