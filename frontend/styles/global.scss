@use '@styles/index' as *;

:root {
  // fonts
  --nj-font--body: 'Roboto Flex', sans-serif;
  --font-sans: var(--nj-font--body);

  // animation speeds
  --nj-speed--xxs: 25ms;
  --nj-speed--xs: 50ms;
  --nj-speed--x: 125ms;
  --nj-speed--xm: 180ms;
  --nj-speed--xl: 225ms;
  --nj-speed--xx: 300ms;
  --nj-speed--xxl: 450ms;
  --nj-speed--xxx: 600ms;
  --nj-speed--4x: 800ms;
  --nj-speed--5x: 1s;

  // opacity
  --nj-opacity-primary: 0.38;
  --nj-opacity-secondary: 0.7;

  // new color pallet
  --nj-primary-neutral-0: #ffffff;
  --nj-primary-neutral-20: #fafafa;
  --nj-primary-neutral-50: #f2f3f3;
  --nj-primary-neutral-80: #eaebec;
  --nj-primary-neutral-100: #e4e6e7;
  --nj-primary-neutral-200: #c9cdcf;
  --nj-primary-neutral-500: #798286;
  --nj-primary-neutral-700: #494e50;
  --nj-primary-neutral-800: #303436;
  --nj-primary-neutral-850: #242728;
  --nj-primary-neutral-880: #1d2020;
  --nj-primary-neutral-900: #181a1b;
  --nj-primary-neutral-980: #050505;

  --nj-primary-hero-500: #005cff;
  --nj-primary-hero-600: #004acc;

  --nj-primary-disco-500: #00c2ff;
  --nj-primary-disco-600: #009bcc;

  --nj-primary-red-500: #e31c2d;
  --nj-primary-yellow-500: #f5ae0a;
  --nj-primary-green-500: #13cc6f;
  --nj-primary-indigo-500: #3d00ff;

  // new color variables light
  --nj-background-body: var(--nj-primary-neutral-20);
  --nj-background-surface-primary: var(--nj-primary-neutral-80);
  --nj-background-surface-secondary: var(--nj-primary-neutral-50);
  --nj-background-surface-tertiary: var(--nj-primary-neutral-200);
  --nj-background-surface-muted: var(--nj-primary-neutral-0);
  --nj-background-surface-muted-90: rgba(255, 255, 255, 0.9);
  --nj-background-surface-reverse: var(--nj-primary-neutral-850);
  --nj-background-surface-primary-hover: rgba(193, 208, 215, 0.4);
  --nj-background-surface-reverse-hover: rgba(40, 55, 62, 0.8);
  --nj-background-surface-selected: rgba(0, 92, 255, 0.08);

  --nj-foreground-primary: var(--nj-primary-neutral-800);
  --nj-foreground-secondary: var(--nj-primary-neutral-500);
  --nj-foreground-primary-muted: var(--nj-primary-neutral-0);
  --nj-foreground-border: var(--nj-primary-neutral-200);
  --nj-foreground-border-muted: var(--nj-primary-neutral-20);

  --nj-accent-primary: var(--nj-primary-hero-500);
  --nj-accent-secondary: var(--nj-primary-hero-600);
  --nj-accent-urgent: var(--nj-primary-red-500);
  --nj-accent-caution: var(--nj-primary-yellow-500);
  --nj-accent-success: var(--nj-primary-green-500);
  --nj-accent-special: var(--nj-primary-indigo-500);

  --nj-shadow: rgba(0, 0, 0, 0.24);
}

.dark {
  // new color variables dark
  --nj-background-body: var(--nj-primary-neutral-980);
  --nj-background-surface-primary: var(--nj-primary-neutral-850);
  --nj-background-surface-secondary: var(--nj-primary-neutral-900);
  --nj-background-surface-tertiary: var(--nj-primary-neutral-700);
  --nj-background-surface-muted: var(--nj-primary-neutral-880);
  --nj-background-surface-muted-90: rgba(29, 32, 32, 0.9);
  --nj-background-surface-reverse: var(--nj-primary-neutral-80);
  --nj-background-surface-primary-hover: rgba(40, 55, 62, 0.8);
  --nj-background-surface-reverse-hover: rgba(193, 208, 215, 0.4);
  --nj-background-surface-selected: rgba(0, 194, 255, 0.2);

  --nj-foreground-primary: var(--nj-primary-neutral-100);
  --nj-foreground-secondary: var(--nj-primary-neutral-500);
  --nj-foreground-primary-muted: var(--nj-primary-neutral-980);
  --nj-foreground-border: var(--nj-primary-neutral-800);
  --nj-foreground-border-muted: var(--nj-primary-neutral-980);

  --nj-accent-primary: var(--nj-primary-disco-500);
  --nj-accent-secondary: var(--nj-primary-disco-600);
  --nj-accent-urgent: var(--nj-primary-red-500);
  --nj-accent-caution: var(--nj-primary-yellow-500);
  --nj-accent-success: var(--nj-primary-green-500);
  --nj-accent-special: var(--nj-primary-indigo-500);
}

body,
p,
span,
div,
a,
button,
input,
textarea,
label,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--nj-font--body) !important;
}

div,
p,
span {
  font-size: 16px !important;
  font-weight: 400 !important;
  line-height: 28px !important;
}

button {
  font-size: 14px !important;
  font-weight: 500 !important;
  line-height: 20px !important;
}

svg {
  flex-shrink: 0;
}
