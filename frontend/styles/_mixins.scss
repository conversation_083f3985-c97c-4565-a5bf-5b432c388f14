@use 'sass:map';
@use 'breakpoints' as *;

/* ≥ min-width */
@mixin from($name) {
  $min: map.get($nj-breakpoints, $name);
  @if $min == null {
    @error "Breakpoint `#{$name}` doesn't exist";
  }

  @media (min-width: #{$min}) {
    @content;
  }
}

/* ≤ max-width */
@mixin until($name) {
  $max: map.get($nj-breakpoints, $name);
  @if $max == null {
    @error "Breakpoint `#{$name}` doesn't exist";
  }

  $limit: $max - 1px;
  @media (max-width: #{$limit}) {
    @content;
  }
}

/* min ≤ viewport ≤ max-1 */
@mixin between($lower, $upper) {
  $min: map.get($nj-breakpoints, $lower);
  $max: map.get($nj-breakpoints, $upper);
  @if $min == null or $max == null {
    @error "Neither `#{$lower}` nor `#{$upper}` doesn't exist";
  }

  $limit: $max - 1px;
  @media (min-width: #{$min}) and (max-width: #{$limit}) {
    @content;
  }
}
