import type { NextConfig } from 'next';
import path from 'node:path';

const nextConfig: NextConfig = {
  webpack: (config) => {
    // This rule prevents issues with pdf.js and canvas
    config.externals = [...(config.externals || []), { canvas: 'canvas' }];

    // Ensure node native modules are ignored
    config.resolve.fallback = {
      ...config.resolve.fallback,
      canvas: false,
    };

    config.resolve.alias = {
      ...(config.resolve.alias || {}),
      '@styles': path.resolve(process.cwd(), 'styles'),
    };

    return config;
  },
  // Add allowedDevOrigins to permit requests from the domain
  allowedDevOrigins: [
    'super-agent-dev.beta.myninja.ai',
    'super-agent-demo.beta.myninja.ai',
  ],
};

export default nextConfig;
