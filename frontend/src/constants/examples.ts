import { ExampleItem } from '@/types';

export const EXAMPLES: ExampleItem[] = [
  {
    id: 1,
    title: 'Interactive author website',
    subtitle:
      'Build an interactive personal website with the list of published books',
    query:
      "Do a deep research about my good friend <PERSON><PERSON><PERSON> about his background, education, accomplishments, books he's written and famous quotes from him. Build an interactive personal website with the list of his books and summaries of the books and Amazon links and reviews for the books so users can easily find his books and purchase them. Make the website exciting and design it such with the latest e-commerce techniques to maximize conversions from user flows. Make the URL public and ready to be shared publicly.",
    exampleUrl:
      'https://ninja-superagent-7728c45e046aeed9784c0bc22dc48b0f.pages.dev/',
  },
  {
    id: 2,
    title: 'Business website & Professional Documents',
    subtitle: 'IPO Roadshow and S1 document',
    query:
      'Cerebras Systems, the AI Chip company is planning to go IPO in 2025. Build a fully interactive and detailed website for the S1 filings with all the required SEC requirements followed. Make the website inspiring for wall street investors, fully interactive, modern and professional so it can be distributed. Double check your answers and also based on the information gathered, create a prospectus document in PDF format for the IPO filing.',
    exampleUrl:
      'https://ninja-superagent-f33f94c3d3f5cf73daeae57d8c7898f7.pages.dev/',
  },
  {
    id: 3,
    title: 'Financial Analysis',
    subtitle: 'Stocks Analysis & Scenario Planning',
    query:
      'Analyze Mag 7 stocks based on the last 4 quarters of performance, give me the maximum details on revenue, break downs, margins, profits in an interactive website and tell me the potential impacts of the Trump Tariff as well as the recently announced co-investments with Saudi Arabia and give me different scenarios and risk levels for me to decide to whether invest $1M in their stock or not. Make the website fancy and cool and very interactive\n',
    exampleUrl:
      'https://ninja-superagent-7105fc1655f7a507e62ce81c00e745da.pages.dev/',
  },
  {
    id: 4,
    title: 'Marketing Strategy',
    subtitle: 'Marketing & User acquisition planning for a local Bar in NYC',
    query:
      "I am the owner of alberts bar near Grand Central in NYC. I'm thinking about expanding my customer base, give me a detailed marketing and user acquisition plan and make it into an interactive website that shows different approaches and potential impacts because I want to double my revenue in the next 6 months without increasing my costs too much. Find my competitors in the area and include in the plan how best I can compete with them. Make the website modern, relevant to the topic, fun & interactive to use and deploy it and give me the link to share it with my colleagues.",
    exampleUrl:
      'https://ninja-superagent-8d245fb192c28b429de9369695cbbd80.pages.dev/',
  },
  {
    id: 5,
    title: 'Planning',
    subtitle: 'Freight logistical Planning',
    query:
      " I want to move a pallet of 200 lbs of items from San Francisco to Atlanta Georgia and also I want to move a similar Pallet from SF to London. Find me some options to optimize costs and speed. I want it done this month in May. Make an interactive website so I can choose various options so I can see trade offs between price & time and make sure it's fully detailed with information from various providers. Make the website slick, fun and interactive so it would show the results for each scenario live. Also for the final results, create a tool tool to download the plan in PDF format",
    exampleUrl:
      'https://ninja-superagent-d52df17a04f81bf44e758893cb5e5d50.pages.dev/',
  },
];
