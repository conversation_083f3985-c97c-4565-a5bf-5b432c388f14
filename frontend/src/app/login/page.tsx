'use client';

import { v4 as uuid_v4 } from 'uuid';
import { Authenticator, useAuthenticator } from '@aws-amplify/ui-react';
import { redirect } from 'next/navigation';
import { useEffect } from 'react';
import Image from 'next/image';
import { NEXT_PUBLIC_MY_NINJA_URL } from '@/lib/envs';

import { ThemeProvider, Theme, useTheme } from '@aws-amplify/ui-react';
import {
  resendSignUpCode,
  ResendSignUpCodeInput,
  signUp,
  type SignUpInput,
  type ConfirmSignUpInput,
  confirmSignUp,
  signInWithRedirect,
  resetPassword,
  ResetPasswordInput,
} from 'aws-amplify/auth';

enum SSOProvider {
  GOOGLE = 'Google',
  AMAZON = 'LoginWithAmazon',
  APPLE = 'SignInWithApple',
  FACEBOOK = 'Facebook',
  MICROSOFT = 'AzureAD',
  LINKEDIN = 'LinkedIn',
}

const SSOProviderTitles = {
  GOOGLE: 'Google',
  AMAZON: 'Amazon',
  APPLE: 'Apple',
  FACEBOOK: 'Facebook',
  MICROSOFT: 'Microsoft',
  LINKEDIN: 'LinkedIn',
};

const CustomSocialProviders = ({ formType = 'signIn' }) => {
  return (
    <div className="w-full px-8 pt-8">
      {Object.keys(SSOProvider).map((provider) => (
        <button
          key={provider}
          onClick={() => {
            signInWithRedirect({
              provider: {
                custom: SSOProvider[provider],
              },
            });
          }}
          className="mb-4 w-full h-12 bg-gray-50 border border-gray-200 cursor-pointer rounded-full flex items-center justify-center gap-2 text-sm text-gray-500 font-medium "
        >
          <Image
            src={`auth/${SSOProvider[provider]}.svg`}
            alt={provider}
            className="w-5 h-5"
            width={24}
            height={24}
          />
          {formType === 'SignIn' ? 'Sign In' : 'Sign Up'} with{' '}
          {SSOProviderTitles[provider]}
        </button>
      ))}

      <hr
        aria-orientation="horizontal"
        className="amplify-divider amplify-divider--horizontal amplify-divider--small"
        data-label="or"
      ></hr>
    </div>
  );
};

const components = {
  Header() {
    return (
      <div className="relative z-10 pt-8 pb-4 max-w-md mx-auto h-full w-full flex flex-col gap-2 items-center justify-center">
        <h1 className="text-3xl md:text-4xl lg:text-5xl font-medium tracking-tighter text-center text-balance text-primary">
          Welcome to Ninja
        </h1>
        <p className="text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight mt-2 mb-6">
          General Agent for your Tasks
        </p>
      </div>
    );
  },
  SignIn: {
    Header() {
      return <CustomSocialProviders formType="SignIn" />;
    },
  },
  SignUp: {
    Header() {
      return <CustomSocialProviders formType="SignUp" />;
    },
  },
};

function CustomAuthenticator() {
  const services = {
    async handleSignUp(input: SignUpInput) {
      const { password, options } = input;
      const customUsername = `Plain_${uuid_v4()}`;
      // Save the custom username in localStorage for later use
      window.localStorage.setItem('_cognito_customUsername', customUsername);
      const customEmail = options?.userAttributes?.email.toLowerCase();
      return signUp({
        username: customUsername,
        password,
        options: {
          ...input.options,
          userAttributes: {
            ...input.options?.userAttributes,
            email: customEmail,
          },
          clientMetadata: {
            verificationCodeOnly: 'true',
          },
        },
      });
    },
    handleResendSignUpCode: async (input: ResendSignUpCodeInput) => {
      const customUsername = window.localStorage.getItem(
        '_cognito_customUsername',
      );
      const resendCodeInput: ResendSignUpCodeInput = {
        ...input,
        username: customUsername as string,
        options: {
          ...input.options,
          clientMetadata: {
            verificationCodeOnly: 'true',
          },
        },
      };
      if (!customUsername) {
        throw new Error('Custom username not found in localStorage');
      }
      return resendSignUpCode(resendCodeInput);
    },
    handleConfirmSignUp: async (input) => {
      const customUsername = window.localStorage.getItem(
        '_cognito_customUsername',
      );
      const confirmSignUpInput: ConfirmSignUpInput = {
        ...input,
        username: customUsername as string,
      };
      if (!customUsername) {
        throw new Error('Custom username not found in localStorage');
      }
      return confirmSignUp(confirmSignUpInput);
    },
    handleForgotPassword: async (input: ResetPasswordInput) => {
      input.options = {
        clientMetadata: {
          verificationCodeOnly: 'true',
        },
      };
      return resetPassword(input);
    },
  };

  const { user } = useAuthenticator((context) => [context.user]);

  useEffect(() => {
    if (user) {
      redirect('/dashboard');
    }
  }, [user]);
  const formFields = {
    signIn: {
      username: {
        placeholder: 'Email address',
        isRequired: true,
        label: 'Email:',
        labelHidden: true,
      },
      password: {
        placeholder: 'Password',
        isRequired: true,
        label: 'Password:',
        labelHidden: true,
      },
    },
    signUp: {
      email: {
        placeholder: 'Email address',
        isRequired: true,
        label: 'Email:',
        labelHidden: true,
        order: 1,
      },
      given_name: {
        placeholder: 'First Name',
        isRequired: true,
        label: 'First Name:',
        labelHidden: true,
        order: 2,
      },
      family_name: {
        placeholder: 'Last Name',
        isRequired: true,
        label: 'Last Name:',
        labelHidden: true,
        order: 3,
      },
      password: {
        placeholder: 'Password',
        isRequired: true,
        label: 'Password:',
        labelHidden: true,
        order: 4,
      },
      confirm_password: {
        placeholder: 'Confirm Password',
        isRequired: true,
        label: 'Confirm Password:',
        labelHidden: true,
        order: 5,
      },
    },
  };
  return (
    <Authenticator
      components={components}
      loginMechanisms={['email']}
      services={services}
      formFields={formFields}
      hideSignUp={true}
    />
  );
}

export default function Login() {
  // useEffect to run this code only in client side since window is undefined in nextjs
  useEffect(() => {
    if (window.location.hostname !== 'localhost') {
      window.location.href = `${NEXT_PUBLIC_MY_NINJA_URL}/login`;
    }
  });

  const { tokens } = useTheme();

  const theme: Theme = {
    name: 'ninja-theme',
    tokens: {
      colors: {
        font: {
          primary: { value: 'oklch(0.556 0 0)' },
        },
        primary: {
          backgroundColor: '#F3F4F6',
        },
      },
      components: {
        divider: {
          label: {
            backgroundColor: '#F3F4F6',
          },
        },
        authenticator: {
          router: {
            boxShadow: `0 0 16px ${tokens.colors.overlay['10']}`,
            borderWidth: '0',
            backgroundColor: '#F3F4F6',
          },
          form: {
            padding: `${tokens.space.medium} ${tokens.space.xl} ${tokens.space.medium}`,
          },
        },
        button: {
          _hover: {
            borderColor: 'oklch(0.708 0 0)',
          },
          primary: {
            backgroundColor: tokens.colors.neutral['100'],
          },
          link: {
            color: 'oklch(0.556 0 0)',
          },
        },
        fieldcontrol: {
          _focus: {
            boxShadow: '0 0 0 2px oklch(0.708 0 0)',
            borderColor: 'oklch(0.708 0 0)',
          },
          borderColor: 'oklch(0.922 0 0)',
          paddingBlockStart: tokens.space.small,
          paddingBlockEnd: tokens.space.small,
        },
        input: {
          color: tokens.colors.neutral['100'],
        },
        tabs: {
          item: {
            color: tokens.colors.neutral['80'],
            backgroundColor: 'oklch(98.46% 0.002 247.84)',
            _hover: {
              color: tokens.colors.neutral['80'],
            },
            _active: {
              borderColor: tokens.colors.neutral['100'],
              backgroundColor: '#F3F4F6',
              color: tokens.colors.neutral['80'],
            },
            _focus: {
              boxShadow: '0 0 0 2px oklch(0.708 0 0)',
              borderColor: 'oklch(0.708 0 0)',
            },
          },
        },
      },
    },
  };
  return (
    <ThemeProvider theme={theme}>
      <Authenticator.Provider>
        <CustomAuthenticator />
      </Authenticator.Provider>
    </ThemeProvider>
  );
}
