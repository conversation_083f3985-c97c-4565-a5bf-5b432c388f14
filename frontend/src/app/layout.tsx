import '@aws-amplify/ui-react/styles.css';
import { ThemeProvider } from '@/components/home/<USER>';
import { siteConfig } from '@/lib/site';
import type { Metadata, Viewport } from 'next';
import { Roboto_Flex } from 'next/font/google';
import '@styles/global.scss';
import './globals.css';
import { Providers } from './providers';
import { Toaster } from '@/components/ui/sonner';
import { Analytics } from '@vercel/analytics/react';
import { GoogleAnalytics } from '@next/third-parties/google';
import { SpeedInsights } from '@vercel/speed-insights/next';
import Script from 'next/script';
import { PublicEnvScript } from 'next-runtime-env';
import Favicon from '/public/favicon.ico';
import ConfigureAmplifyClientSide from '@/components/ConfigureAmplify';

const robotoFlex = Roboto_Flex({
  variable: '--nj-font--body',
  subsets: ['latin'],
  display: 'swap',
});

export const viewport: Viewport = {
  themeColor: 'black',
};

export const metadata: Metadata = {
  metadataBase: new URL(siteConfig.url),
  title: {
    default: siteConfig.name,
    template: `%s - ${siteConfig.name}`,
  },
  description:
    'NinjaTech AI is your all-in-one AI assistant that helps you accomplish tasks with ease. Fast and affordable, with unlimited tasks powered by best-in-class AI models.',
  keywords: [
    'AI',
    'artificial intelligence',
    'AI assistant',
    'AI tools',
    'compound AI',
    'agentic AI',
    'research',
    'data analysis',
    'image generation',
    'code generation',
  ],
  authors: [{ name: 'NinjaTech AI Team', url: 'https://www.ninjatech.ai' }],
  creator: 'NinjaTech AI',
  publisher: 'NinjaTech AI',
  category: 'Technology',
  applicationName: 'NinjaTech AI',
  formatDetection: {
    telephone: false,
    email: false,
    address: false,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
  openGraph: {
    title: 'NinjaTech AI - Your Agentic All-in-One AI Assistant',
    description:
      'Fast and affordable, with unlimited tasks. Powered by best-in-class AI models.',
    url: siteConfig.url,
    siteName: 'NinjaTech AI',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'NinjaTech AI - Your Agentic All-in-One AI Assistant',
    description:
      'Fast and affordable, with unlimited tasks. Powered by best-in-class AI models.',
    creator: '@ninjatechai',
    site: '@ninjatechai',
  },
  icons: [
    {
      rel: 'icon',
      url: Favicon.src,
    },
  ],
  alternates: {
    canonical: siteConfig.url,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
      suppressHydrationWarning
      className={`${robotoFlex.variable}`}
    >
      <head>
        <PublicEnvScript />
        {/* Google Tag Manager */}
        <Script id="google-tag-manager" strategy="afterInteractive">
          {`(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','GTM-PCHSN4M2');`}
        </Script>
        {/* End Google Tag Manager */}
      </head>

      <body className="antialiased font-sans bg-background">
        {/* Google Tag Manager (noscript) */}
        <noscript>
          <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-PCHSN4M2"
            height="0"
            width="0"
            style={{ display: 'none', visibility: 'hidden' }}
          />
        </noscript>
        {/* End Google Tag Manager (noscript) */}
        <ConfigureAmplifyClientSide />
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
          enableColorScheme
        >
          <Providers>
            {children}
            <Toaster />
          </Providers>
          <Analytics />
          <GoogleAnalytics gaId="G-6ETJFB3PT3" />
          <SpeedInsights />
        </ThemeProvider>
      </body>
    </html>
  );
}
