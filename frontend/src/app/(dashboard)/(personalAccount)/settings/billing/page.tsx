import { createClient } from '@/lib/supabase/server';
import AccountBillingStatus from '@/components/billing/account-billing-status';
import { NEXT_PUBLIC_URL } from '@/lib/envs';

export default async function PersonalAccountBillingPage() {
  const supabaseClient = await createClient();
  const { data: personalAccount } = await supabaseClient.rpc(
    'get_personal_account',
  );

  return (
    <div>
      <AccountBillingStatus
        accountId={personalAccount.account_id}
        returnUrl={`${NEXT_PUBLIC_URL}/settings/billing`}
      />
    </div>
  );
}
