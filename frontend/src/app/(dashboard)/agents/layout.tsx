import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Agent Conversation | NinjaTech AI',
  description: 'Interactive agent conversation powered by NinjaTech AI',
  openGraph: {
    title: 'Agent Conversation | NinjaTech AI',
    description: 'Interactive agent conversation powered by NinjaTech AI',
    type: 'website',
  },
};

export default function AgentsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
