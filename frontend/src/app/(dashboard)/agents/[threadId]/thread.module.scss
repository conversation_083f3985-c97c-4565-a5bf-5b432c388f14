@use '@styles/index' as *;

.attachmentContainer {
  margin-top: 16px;
}

.gridWrapper {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;

  @include from(tablet) {
    &.shouldBeDisplayedInTwoColumns {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

.attachment {
  @extend %flex-between;

  max-width: 300px;
  border-radius: 12px;
  background: var(--nj-background-surface-muted);
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.32);
  padding: 8px;
  color: var(--nj-foreground-primary);
}

.fileIconWrapper {
  padding: 8px;
  color: var(--nj-accent-primary);
}

.iconWrapper {
  padding: 8px;
}

.attachmentWrapper {
  @extend %flex-row;
  min-width: 0;
}

.attachmentTitleWrapper {
  overflow: hidden;
}

.attachmentTitle {
  @extend %sub-header-1-alt;

  overflow: hidden;
  text-overflow: ellipsis;
}

.attachmentCaption {
  @extend %caption-1;

  color: var(--nj-foreground-secondary);
}

.attachmentTitle,
.attachmentCaption {
  text-align: left;
  white-space: nowrap;
}
