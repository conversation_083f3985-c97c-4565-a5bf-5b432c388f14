'use client';

import { useEffect } from 'react';
import { SidebarLeft } from '@/components/sidebar/sidebar-left';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
// import { PricingAlert } from "@/components/billing/pricing-alert"
import { useAccounts } from '@/hooks/use-accounts';
import { useAuth } from '@/components/AuthProvider';
import { useRouter } from 'next/navigation';
import { DeleteOperationProvider } from '@/contexts/DeleteOperationContext';
import { StatusOverlay } from '@/components/ui/status-overlay';
import { NinjaLoader } from '@/components/ui/NinjaLoader';
import { SidebarRight } from '@/components/sidebar/SidebarRight';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  NEXT_PUBLIC_MY_NINJA_URL,
  NEXT_PUBLIC_COGNITO_COOKIES_DOMAIN,
} from '@/lib/envs';
import Cookies from 'js-cookie';

const SUPER_NINJA_RETURN_URL_COOKIE_NAME = '_superNinjaReturnUrl';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  // const [showPricingAlert, setShowPricingAlert] = useState(false)
  const { data: accounts } = useAccounts();
  const personalAccount = accounts?.find((account) => account.personal_account);
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const isMobile = useIsMobile();

  // Check authentication status
  useEffect(() => {
    if (!isLoading && !user) {
      // remember the URL before redirecting. Myninja should redirect back after successful authorization.
      Cookies.set(SUPER_NINJA_RETURN_URL_COOKIE_NAME, window.location.href, {
        domain: NEXT_PUBLIC_COGNITO_COOKIES_DOMAIN,
      });
      window.location.href = `${NEXT_PUBLIC_MY_NINJA_URL}/login`;
    }
  }, [user, isLoading, router]);

  // Show loading state while checking auth or health
  if (isLoading) {
    return <NinjaLoader />;
  }

  // Don't render anything if not authenticated
  if (!user) {
    return null;
  }

  return (
    <DeleteOperationProvider>
      <SidebarProvider>
        <SidebarLeft />
        <SidebarInset>
          <div className="bg-background">{children}</div>
        </SidebarInset>
        {!isMobile && <SidebarRight />}

        {/* <PricingAlert
          open={showPricingAlert}
          onOpenChange={setShowPricingAlert}
          closeable={false}
          accountId={personalAccount?.account_id}
          /> */}

        {/* Status overlay for deletion operations */}
        <StatusOverlay />
      </SidebarProvider>
    </DeleteOperationProvider>
  );
}
