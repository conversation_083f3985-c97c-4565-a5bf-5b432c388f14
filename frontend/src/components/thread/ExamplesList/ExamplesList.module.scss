@use '@styles/index' as *;

.root {
  @extend %flex-row-center;
  gap: 16px;
  flex-wrap: wrap;
}

.exampleCard {
  @extend %flex-column;
  gap: 0;
  width: 248px;
  min-height: 285px;
  border-radius: 16px;
  border: 1px solid var(--nj-foreground-border);

  .hoveredBlock {
    @extend %flex-column-center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
  }

  &:hover {
    .hoveredBlock {
      display: flex;
    }
  }
}

.topSection {
  @extend %flex-column;
  height: 160px;
  position: relative;
}

.bottomSection {
  @extend %flex-column;
  gap: 4px;
  padding: 16px 24px;
}

.imageBlock {
  width: 100%;
  height: 100%;
  background-size: cover;
  object-fit: cover;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}

.queryBlock {
  @extend %flex-column;
  background-color: var(--nj-background-surface-secondary);
  color: var(--nj-foreground-secondary);
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 24px;
}

.queryText {
  @extend %header-3;
  font-weight: 500;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
}

.title {
  @extend %caption-1;
  color: var(--nj-foreground-secondary);
  margin: 0;
}

.subtitle {
  @extend %sub-header-1-alt;
  margin: 0;
}

.button,
.exampleButton {
  @extend %button;
  text-decoration: none;
  width: 105px;
  padding: 6px 12px;
  align-items: center;
  border-radius: 20px;

  &:hover {
    cursor: pointer;
  }
}

.exampleButton {
  display: block;
  background-color: var(--nj-background-surface-primary);
  color: var(--nj-foreground-primary);
}
