'use client';

import styles from './ExamplesList.module.scss';
import { EXAMPLES, SVG_SIZE_S } from '@/constants';
import { ChatCircleDots } from '@phosphor-icons/react';
import { Button } from '@/components/ui/Button/index';

interface ExamplesListProps {
  onTryNowClick: (value: string) => void;
  isDisabled: boolean;
}

export const ExamplesList = ({
  onTryNowClick,
  isDisabled,
}: ExamplesListProps) => {
  return (
    <div className={styles.root}>
      {EXAMPLES.map((item) => (
        <div className={styles.exampleCard} key={item.id}>
          <div className={styles.topSection}>
            {item.imageUrl ? (
              <div
                style={{ backgroundImage: `url(${item.imageUrl})` }}
                className={styles.imageBlock}
              />
            ) : (
              <div className={styles.queryBlock}>
                <ChatCircleDots size={SVG_SIZE_S} weight="fill" />
                <span className={styles.queryText}>{item.query}</span>
              </div>
            )}
            <div className={styles.hoveredBlock}>
              <Button
                color="black"
                className={styles.button}
                onClick={() => onTryNowClick(item.query)}
                disabled={isDisabled}
              >
                Try now
              </Button>
              <a
                target="_blank"
                rel="noreferrer"
                href={item.exampleUrl}
                className={styles.exampleButton}
              >
                See example
              </a>
            </div>
          </div>
          <div className={styles.bottomSection}>
            <h6 className={styles.title}>{item.title}</h6>
            <h4 className={styles.subtitle}>{item.subtitle}</h4>
          </div>
        </div>
      ))}
    </div>
  );
};
