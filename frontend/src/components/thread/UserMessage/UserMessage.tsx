'use client';

import { ReactNode } from 'react';
import { Markdown } from '@/components/ui/markdown';
import styles from './UserMessage.module.scss';

interface UserMessageProps {
  cleanContent: string;
  children?: ReactNode;
}

export const UserMessage = ({ cleanContent, children }: UserMessageProps) => {
  return (
    <div className={styles.root}>
      <div className={styles.container}>
        {cleanContent && <Markdown>{cleanContent}</Markdown>}

        {children}
      </div>
    </div>
  );
};
