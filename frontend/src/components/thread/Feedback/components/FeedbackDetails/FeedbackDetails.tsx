'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/Button/Button';
import { TextareaSimple } from '@/components/ui/TextareaSimple';
import styles from './FeedbackDetails.module.scss';
import { createAgentRunFeedback } from '@/lib/api';

interface FeedbackDetailsProps {
  feedbackId?: string;
  groupAgentRunId: string;
  currentRating: number | null;
  open: boolean;
  onToggle: () => void;
}

export const FeedbackDetails = ({
  groupAgentRunId,
  currentRating,
  open,
  onToggle,
}: FeedbackDetailsProps) => {
  const [reason, setReason] = useState<string>('');

  const handleToggle = () => {
    setReason('');
    onToggle();
  };

  const handleDetailsSubmit = async () => {
    const data = {
      rating: {
        rating: currentRating,
        reason,
      },
      agent_run_id: groupAgentRunId,
    };

    try {
      await createAgentRunFeedback(data);
    } catch (error) {
      // TODO(olha): show a toast
    }

    handleToggle();
  };

  return (
    <Dialog open={open} onOpenChange={handleToggle}>
      <DialogContent className={styles.root}>
        <DialogHeader>
          <DialogTitle>Tell us why?</DialogTitle>
        </DialogHeader>

        <TextareaSimple
          value={reason}
          onChange={setReason}
          minRows={4}
          maxRows={4}
        />

        <DialogFooter>
          <Button onClick={handleDetailsSubmit}>Submit</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
