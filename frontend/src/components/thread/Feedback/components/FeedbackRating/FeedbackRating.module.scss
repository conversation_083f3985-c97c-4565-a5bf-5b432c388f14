@use '@styles/index' as *;

.root {
  @extend %flex-column-center;
  width: 100%;

  p {
    margin: 0;
  }
}

.content {
  @extend %flex-row;
  gap: 16px;
}

.title {
  @extend %sub-header-1;
}

.moreButton {
  padding: 0;
}

.ratingWrapper {
  @extend %flex-row;
  gap: 0;
}

.ratingButton {
  @extend %flex-column;
  flex: 1 1 70px;
  gap: 0;
  padding: 4px;
  color: var(--nj-foreground-secondary);
  width: 70px;

  &.selected {
    color: var(--nj-accent-secondary);
  }
}

.iconTitle {
  @extend %caption-1;
}

.modalContainer {
  @extend %flex-column;
  gap: 24px;
}

.modalTitle {
  @extend %header-2;
  margin: 0;
}

.submitButton {
  margin: 0 auto;
}
