'use client';

import { useState } from 'react';
import cn from 'classnames';
import styles from './FeedbackRating.module.scss';
import { Button } from '@/components/ui/Button/Button';
import { SVG_SIZE_L } from '@/constants';
import {
  SmileyIcon,
  Icon,
  SmileyWinkIcon,
  SmileyMehIcon,
  SmileySadIcon,
  SmileyAngryIcon,
} from '@phosphor-icons/react';
import { useVisible } from '@/hooks';
import { FeedbackDetails } from '../FeedbackDetails';
import { createAgentRunFeedback } from '@/lib/api';
import { AgentRunWithFeedback } from '@/types';

const FEEDBACK_MAP: { rating: number; title: string; IconComponent: Icon }[] = [
  {
    rating: 1,
    title: 'Incorrect',
    IconComponent: SmileyAngryIcon,
  },
  {
    rating: 2,
    title: 'Poor',
    IconComponent: SmileySadIcon,
  },
  {
    rating: 3,
    title: 'Fair',
    IconComponent: SmileyMehIcon,
  },
  {
    rating: 4,
    title: 'Good',
    IconComponent: SmileyIcon,
  },
  {
    rating: 5,
    title: 'Superb',
    IconComponent: SmileyWinkIcon,
  },
];

interface FeedbackRatingProps {
  groupAgentRun: AgentRunWithFeedback;
}

export const FeedbackRating = ({ groupAgentRun }: FeedbackRatingProps) => {
  const { isVisible, onVisibilitySet, onVisibilityToggle } = useVisible();
  const [currentRating, setCurrentRating] = useState<number | null>(
    groupAgentRun?.feedback?.data?.rating?.rating || null,
  );
  const [loading, setLoading] = useState<boolean>(false);

  const handleRatingClick = async (rating: number) => {
    const data = {
      rating: {
        rating,
      },
      agent_run_id: groupAgentRun?.id,
    };

    setCurrentRating((prev) => (prev === rating ? null : rating));
    setLoading(true);
    try {
      await createAgentRunFeedback(data);
    } catch (error) {
      // TODO(olha): show a toast
      setCurrentRating(currentRating);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div className={styles.root}>
        <div className={styles.content}>
          <p className={styles.title}>Rate the response</p>

          <Button
            onClick={onVisibilitySet}
            disabled={currentRating === null || loading}
            color="link"
            data-e2e="task-rating-text-button"
          >
            Tell us why?
          </Button>
        </div>

        <div className={styles.ratingWrapper}>
          {FEEDBACK_MAP.map(({ rating, IconComponent, title }) => {
            const isSelected = currentRating === rating;

            return (
              <Button
                className={cn(styles.ratingButton, {
                  [styles.selected]: isSelected,
                })}
                key={rating}
                color="transparent"
                onClick={() => handleRatingClick(rating)}
                data-e2e={`task-rating-${rating}`}
                disabled={loading}
              >
                <IconComponent
                  weight={isSelected ? 'fill' : 'regular'}
                  size={SVG_SIZE_L}
                />
                <span className={styles.iconTitle}>{title}</span>
              </Button>
            );
          })}
        </div>
      </div>

      <FeedbackDetails
        groupAgentRunId={groupAgentRun?.id}
        currentRating={currentRating}
        open={isVisible}
        onToggle={onVisibilityToggle}
      />
    </>
  );
};
