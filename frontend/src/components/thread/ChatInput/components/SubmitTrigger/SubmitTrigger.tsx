'use client';

import { FormEvent } from 'react';
import { StopResponseButton } from '../StopResponseButton';
import { SendMessageButton } from '../SendMessageButton';

interface SubmitTriggerProps {
  isLoading: boolean;
  isAgentRunning: boolean;
  disabled: boolean;
  onSubmit: (e: FormEvent) => void;
  onStopAgent: () => void;
}

export const SubmitTrigger = ({
  isLoading,
  isAgentRunning,
  disabled,
  onSubmit,
  onStopAgent,
}: SubmitTriggerProps) => {
  return (
    <>
      {isAgentRunning || isLoading ? (
        <StopResponseButton
          onStop={onStopAgent}
          disabled={disabled}
          isLoading={isLoading}
        />
      ) : (
        <SendMessageButton onSubmit={onSubmit} disabled={disabled} />
      )}
    </>
  );
};
