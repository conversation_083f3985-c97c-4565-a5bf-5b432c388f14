'use client';

import { FormEvent } from 'react';
import { PaperPlaneRightIcon } from '@phosphor-icons/react';
import { Button } from '@/components/ui/Button/Button';
import { SVG_SIZE_M } from '@/constants';

interface SendMessageButtonProps {
  onSubmit: (e: FormEvent) => void;
  disabled?: boolean;
}

export const SendMessageButton = ({
  disabled,
  onSubmit,
}: SendMessageButtonProps) => {
  return (
    <Button
      type="submit"
      shape="round"
      color={disabled ? 'transparent' : 'primary'}
      disabled={disabled}
      onClick={onSubmit}
    >
      <PaperPlaneRightIcon
        weight={disabled ? 'regular' : 'fill'}
        size={SVG_SIZE_M}
      />
    </Button>
  );
};
