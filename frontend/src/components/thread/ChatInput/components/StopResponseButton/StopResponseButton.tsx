'use client';

import { StopCircleIcon } from '@phosphor-icons/react';
import { Button } from '@/components/ui/Button/Button';
import { SVG_SIZE_M } from '@/constants';
import styles from './StopResponseButton.module.scss';

interface StopResponseButtonProps {
  isLoading?: boolean;
  disabled?: boolean;
  onStop: () => void;
}

export const StopResponseButton = ({
  isLoading,
  disabled,
  onStop,
}: StopResponseButtonProps) => {
  return (
    <Button
      type="submit"
      shape="round"
      color="transparent"
      disabled={disabled}
      onClick={onStop}
      className={styles.root}
    >
      <StopCircleIcon size={SVG_SIZE_M} weight="fill" />
      {isLoading && <span className={styles.spinner} />}
    </Button>
  );
};
