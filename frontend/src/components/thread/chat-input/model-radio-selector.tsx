'use client';

import { useState } from 'react';
import { GearIcon } from '@phosphor-icons/react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Button } from '@/components/ui/Button/Button';
import { Label } from '@/components/ui/label';
import { MODEL_OPTIONS, SVG_SIZE_M } from '@/constants';

interface ModelRadioSelectorProps {
  selectedModel: string;
  onModelChange: (model: string) => void;
}

export const ModelRadioSelector = ({
  selectedModel,
  onModelChange,
}: ModelRadioSelectorProps) => {
  const [showModelDialog, setShowModelDialog] = useState<boolean>(false);

  const handleModelChange = (value: string) => {
    onModelChange(value);
  };

  return (
    <Dialog open={showModelDialog} onOpenChange={setShowModelDialog}>
      <DialogTrigger asChild>
        <Button color="gray">
          <GearIcon size={SVG_SIZE_M} />
          Select Model
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-md" aria-describedby={undefined}>
        <DialogHeader>
          <DialogTitle className="text-sm font-medium">
            Select Model
          </DialogTitle>
        </DialogHeader>

        <div className="p-4">
          <RadioGroup
            defaultValue={selectedModel}
            onValueChange={handleModelChange}
            className="grid gap-2"
          >
            {MODEL_OPTIONS.map((option) => (
              <div
                key={option.id}
                className="flex items-center space-x-2 rounded-md px-3 py-2 cursor-pointer hover:bg-accent"
              >
                <RadioGroupItem value={option.id} id={option.id} />
                <Label
                  htmlFor={option.id}
                  className="flex-1 cursor-pointer text-sm font-normal"
                >
                  {option.label}
                </Label>
                {selectedModel === option.id && (
                  <span className="text-xs text-muted-foreground">Active</span>
                )}
              </div>
            ))}
          </RadioGroup>
        </div>

        <DialogFooter>
          <Button onClick={() => setShowModelDialog(false)}>Done</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
