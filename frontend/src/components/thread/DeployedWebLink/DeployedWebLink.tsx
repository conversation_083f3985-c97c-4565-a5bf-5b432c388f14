'use client';

import { SVG_SIZE_M } from '@/constants';
import { GlobeSimpleIcon } from '@phosphor-icons/react';
import styles from './DeployedWebLink.module.scss';

interface DeployedWebLinkProps {
  href: string;
}

export const DeployedWebLink = ({ href }: DeployedWebLinkProps) => {
  return (
    <a href={href} target="_blank" rel="noreferrer" className={styles.root}>
      <span className={styles.icon}>
        <GlobeSimpleIcon weight="fill" size={SVG_SIZE_M} />
      </span>
      <span className={styles.content}>
        <span className={styles.title}>ninjatech-ai</span>
        <span className={styles.caption}>{href}</span>
      </span>
    </a>
  );
};
