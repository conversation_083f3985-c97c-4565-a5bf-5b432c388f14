@use '@styles/index' as *;

.root {
  @extend %flex-row;

  margin-top: 8px;
  padding: 8px;
  border-radius: 12px;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.32);
  background-color: var(--nj-background-body);
  width: 100%;
  max-width: 480px;
}

.icon {
  flex-shrink: 0;
  color: var(--nj-accent-primary);
  padding: 8px;
}

.content {
  @extend %flex-column;
  gap: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.title {
  @extend %sub-header-1-alt;
}

.caption {
  @extend %caption-1;
  overflow: hidden;
  color: var(--nj-foreground-secondary);
  text-overflow: ellipsis;
  white-space: nowrap;
}
