import { Markdown } from '@/components/ui/markdown';
import { ExternalLink } from 'lucide-react';
import { cleanUrl } from './utils';

interface WebScrapeToolTextItemProps {
  url?: string;
  title?: string;
  text?: string;
}

export const WebScrapeToolTextItem = ({
  url,
  title,
  text,
}: WebScrapeToolTextItemProps) => (
  <article className="mb-6">
    <header className="pb-2 space-y-0.5">
      <div className="text-xs text-zinc-500 dark:text-zinc-400 truncate">
        {cleanUrl(url)}
      </div>
      <a
        href={url}
        target="_blank"
        rel="noopener noreferrer"
        className="flex items-center gap-1 text-sm font-medium text-blue-600 dark:text-blue-400 hover:underline"
      >
        {title}
        <ExternalLink className="h-3 w-3 opacity-60" />
      </a>
    </header>

    <Markdown>{text ?? 'No content extracted'}</Markdown>
  </article>
);
