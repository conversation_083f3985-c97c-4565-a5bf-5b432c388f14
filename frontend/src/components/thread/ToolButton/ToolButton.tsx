'use client';

import { Button } from '@/components/ui/Button/Button';
import { SVG_SIZE_S } from '@/constants';
import { getToolIcon } from '@/lib/utils/threadUtils';
import { ArrowClockwiseIcon } from '@phosphor-icons/react';
import { useMemo } from 'react';
import styles from './ToolButton.module.scss';
import classNames from 'classnames';

interface ToolButtonProps {
  toolName?: string;
  paramDisplay: string;
  inProgress?: boolean;
  onClick?: () => void;
}

export const ToolButton = ({
  toolName,
  paramDisplay,
  inProgress,
  onClick,
}: ToolButtonProps) => {
  const IconComponent = useMemo(
    () => (inProgress ? ArrowClockwiseIcon : getToolIcon(toolName || '')),
    [inProgress, toolName],
  );

  return (
    <Button
      size="small"
      color={inProgress ? 'tertiary' : 'gray'}
      onClick={onClick}
    >
      <IconComponent
        className={classNames({ [styles.iconSpin]: inProgress })}
        size={SVG_SIZE_S}
      />

      {toolName && <span className={styles.title}>{toolName}</span>}

      <span className={styles.caption}>{paramDisplay}</span>
    </Button>
  );
};
