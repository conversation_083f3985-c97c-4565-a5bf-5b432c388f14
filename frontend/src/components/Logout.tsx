// components/Logout.tsx

'use client';

import { signOut } from 'aws-amplify/auth';
import { useRouter } from 'next/navigation';
import { NEXT_PUBLIC_MY_NINJA_URL } from '@/lib/envs';

export default function Logout() {
  const router = useRouter();

  return (
    <button
      onClick={async () => {
        await signOut();
        window.location.href = `${NEXT_PUBLIC_MY_NINJA_URL}/`;
      }}
      className="px-2 bg-white text-black"
    >
      Sign out
    </button>
  );
}
