'use client';

import { Amplify, ResourcesConfig } from 'aws-amplify';
import { getAmplifyConfig } from '@/lib/utils/amplify-config';

import { cognitoUserPoolsTokenProvider } from 'aws-amplify/auth/cognito';
import { CookieStorage } from '@aws-amplify/core';
import { NEXT_PUBLIC_COGNITO_COOKIES_DOMAIN } from '@/lib/envs';

Amplify.configure(getAmplifyConfig() as ResourcesConfig, { ssr: true });
cognitoUserPoolsTokenProvider.setKeyValueStorage(
  new CookieStorage({
    domain: NEXT_PUBLIC_COGNITO_COOKIES_DOMAIN,
    path: '/',
    expires: 365, // Cookie expiration in days
    secure: NEXT_PUBLIC_COGNITO_COOKIES_DOMAIN !== 'localhost', // only for test on localhost
  }),
);
// this function even empty is required by Amplify
export default function ConfigureAmplifyClientSide() {
  return null;
}
