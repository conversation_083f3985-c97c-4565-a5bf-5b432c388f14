import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { createClient } from '@/lib/supabase/client';
import { SupabaseClient } from '@supabase/supabase-js';
import { type AuthSession, fetchAuthSession, Hub } from '@aws-amplify/core';
import { signOut as cognitoSignOut } from 'aws-amplify/auth';
import { getUserFromAmplifySession } from '@/lib/utils/amplify-client-utils';
type AuthContextType = {
  supabase: SupabaseClient;
  session: AuthSession | null;
  user: string | null;
  isLoading: boolean;
  signOut: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const supabase = createClient();

  const [session, setSession] = useState<AuthSession | null>(null);
  const [user, setUser] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const hubListenerCancelToken = Hub.listen('auth', async ({ payload }) => {
      switch (payload.event) {
        case 'signedIn':
          const currentSession = await fetchAuthSession();
          setSession(currentSession);
          const user = await getUserFromAmplifySession();
          setUser(user?.ninja_user_id || null);

          // Delete the custom username from localStorage after sign-in (it was used for email signup process to store custom username)
          window.localStorage.removeItem('_cognito_customUsername');

          break;
        case 'signedOut':
          setSession(null);
          setUser(null);
          break;
        case 'tokenRefresh':
          const refreshedSession = await fetchAuthSession();
          setSession(refreshedSession);
          break;
        case 'tokenRefresh_failure':
          console.warn('Failure while refreshing auth tokens.');
          break;
      }
    });

    return () => {
      hubListenerCancelToken?.();
    };
  }, []);

  useEffect(() => {
    const getInitialSession = async () => {
      const currentSession = await fetchAuthSession();
      setSession(currentSession);
      if (!currentSession?.tokens?.accessToken) {
        console.warn('No access token found, user is not authenticated.');
        setIsLoading(false);
        return;
      }
      const user = await getUserFromAmplifySession();

      setUser(user?.ninja_user_id);
      setIsLoading(false);
    };

    getInitialSession();
  }, []);

  useEffect(() => {
    const checkAccountExists = async () => {
      const { data, error } = await supabase.rpc('get_accounts');

      if (data && data.length === 0) {
        console.warn('No accounts found, creating a new account...');

        const sessionUser = await getUserFromAmplifySession();

        const { error } = await supabase.from('users').insert({
          id: sessionUser.ninja_user_id,
          role: 'authenticated',
          aud: 'authenticated',
          name: sessionUser.name,
          email: sessionUser.email,
        });
      }
    };
    if (user) {
      checkAccountExists();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  const signOut = async () => {
    await cognitoSignOut();
  };

  const value = {
    supabase,
    session,
    user,
    isLoading,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
