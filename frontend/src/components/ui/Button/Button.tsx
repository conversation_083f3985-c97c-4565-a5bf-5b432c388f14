'use client';

import { forwardRef, ButtonHTMLAttributes } from 'react';
import classNames from 'classnames';
import styles from './Button.module.scss';

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  size?: 'small' | 'medium';
  shape?: 'regular' | 'round';
  color?:
    | 'primary'
    | 'secondary'
    | 'transparent'
    | 'link'
    | 'tertiary'
    | 'tertiary-outline'
    | 'tertiary-neutral'
    | 'gray'
    | 'black'
    | 'white'
    | 'error'
    | 'active-transparent';
  className?: string;
  fullWidth?: boolean;
  fullWidthWithLeftAlign?: boolean;
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      size = 'medium',
      shape = 'regular',
      color = 'primary',
      className,
      fullWidth = false,
      fullWidthWithLeftAlign = false,
      children,
      ...props
    },
    ref,
  ) => {
    const colorWithBorder =
      color === 'secondary' || color === 'tertiary-outline';

    return (
      <button
        ref={ref}
        {...props}
        className={classNames(
          styles.root,
          {
            [styles.primary]: color === 'primary',
            [styles.secondary]: color === 'secondary',
            [styles.transparent]: color === 'transparent',
            [styles.link]: color === 'link',
            [styles.tertiary]: color === 'tertiary',
            [styles.tertiaryOutline]: color === 'tertiary-outline',
            [styles.tertiaryNeutral]: color === 'tertiary-neutral',
            [styles.gray]: color === 'gray',
            [styles.black]: color === 'black',
            [styles.white]: color === 'white',
            [styles.error]: color === 'error',
            [styles.activeTransparent]: color === 'active-transparent',
            [styles.fullWidth]: fullWidth,
            [styles.fullWidthWithLeftAlign]: fullWidthWithLeftAlign,
            // next classes are combinations of main properties. To make all buttons with the same sizes and available to rewrite in a simple way
            [styles.roundSmall]: shape === 'round' && size === 'small',
            [styles.regularSmall]: shape === 'regular' && size === 'small',
            [styles.roundMedium]: shape === 'round' && size === 'medium',
            [styles.regularMedium]: shape === 'regular' && size === 'medium',
            [styles.roundSmallWithBorder]:
              colorWithBorder && shape === 'round' && size === 'small',
            [styles.regularSmallWithBorder]:
              colorWithBorder && shape === 'regular' && size === 'small',
            [styles.roundMediumWithBorder]:
              colorWithBorder && shape === 'round' && size === 'medium',
            [styles.regularMediumWithBorder]:
              colorWithBorder && shape === 'regular' && size === 'medium',
          },
          className,
        )}
      >
        {children}
      </button>
    );
  },
);

Button.displayName = 'Button';
