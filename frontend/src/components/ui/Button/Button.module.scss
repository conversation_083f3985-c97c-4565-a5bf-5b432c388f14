@use '@styles/index' as *;

.root {
  @extend %flex-center;
  @extend %button;

  position: relative;
  color: var(--nj-foreground-primary);
  border: none;
  outline: none;
  cursor: pointer;

  &:disabled {
    opacity: var(--nj-opacity-primary);
    cursor: not-allowed;
  }
}

.roundSmall,
.roundMedium {
  border-radius: 50%;
}

.roundSmall {
  padding: 6px;
}

.roundMedium {
  padding: 10px;
}

.regularSmall,
.regularMedium {
  white-space: nowrap;
}

.regularSmall {
  border-radius: 12px;
  padding: 6px 12px;
}

.regularMedium {
  border-radius: 20px;
  padding: 10px 16px;
}

.roundSmallWithBorder,
.regularSmallWithBorder,
.roundMediumWithBorder,
.regularMediumWithBorder {
  border: 1px solid;
}

.roundSmallWithBorder {
  padding: 5px;
}

.regularSmallWithBorder {
  padding: 5px 11px;
}

.roundMediumWithBorder {
  padding: 9px;
}

.regularMediumWithBorder {
  padding: 9px 15px;
}

// TODO: update to new color pallet
.primary {
  background-color: var(--nj-accent-primary);
  color: var(--nj-background-body);
}

.secondary {
  background: transparent;
  border-color: var(--nj-foreground-border);
}

.gray {
  background-color: var(--nj-background-surface-primary);
}

// TODO: update to new color pallet
.black {
  background-color: var(--nj-foreground-primary);
  color: var(--nj-background-surface-muted);
}

// TODO: update to new color pallet
.white {
  background-color: var(--nj-background-body);
  color: var(--nj-foreground-primary);
}

.activeTransparent {
  background-color: var(--nj-background-surface-selected);
  color: var(--nj-accent-secondary);
}

.tertiary,
.tertiaryOutline,
.tertiaryNeutral {
  background-color: var(--nj-background-surface-selected);
}

.tertiary,
.tertiaryOutline {
  color: var(--nj-accent-primary);
}

.tertiaryOutline {
  border-color: var(--nj-accent-secondary);
}

.transparent {
  background: transparent;
}

.error {
  background-color: var(--nj-accent-urgent);
  color: var(--nj-background-body);
}

.link {
  padding: 0;
  background: transparent;
  color: var(--nj-accent-primary);
}

.fullWidth,
.fullWidthWithLeftAlign {
  width: 100%;
}

.fullWidthWithLeftAlign {
  justify-content: flex-start;
}
