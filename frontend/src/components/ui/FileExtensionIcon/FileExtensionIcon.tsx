import { SVG_SIZE_L } from '@/constants';
import {
  MicrosoftExcelLogoIcon,
  Icon,
  FileCsvIcon,
  FilePptIcon,
  FileIcon,
  FileTextIcon,
  FileMdIcon,
  FilePdfIcon,
  FileDocIcon,
  FileHtmlIcon,
  FileCssIcon,
  FileCodeIcon,
} from '@phosphor-icons/react';
import { useMemo } from 'react';

interface FileExtensionIconProps {
  extension: string;
}

export const FileExtensionIcon = ({ extension }: FileExtensionIconProps) => {
  const IconComponent: Icon = useMemo(() => {
    switch (extension) {
      case 'xls':
      case 'xlsx':
      case 'xlsm':
      case 'xlsb':
      case 'xlt':
      case 'xltx':
      case 'xltm':
        return MicrosoftExcelLogoIcon;

      case 'csv':
        return FileCsvIcon;

      case 'ppt':
      case 'pptx':
      case 'pps':
      case 'ppsx':
      case 'pot':
      case 'potx':
        return FilePptIcon;

      case 'txt':
        return FileTextIcon;

      case 'md':
      case 'markdown':
        return FileMdIcon;

      case 'pdf':
        return FilePdfIcon;

      case 'doc':
      case 'docx':
      case 'dot':
      case 'dotx':
      case 'rtf':
      case 'odt':
        return FileDocIcon;

      case 'html':
        return FileHtmlIcon;

      case 'css':
      case 'scss':
      case 'sass':
      case 'less':
        return FileCssIcon;

      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
      case 'json':
      case 'xml':
      case 'yml':
      case 'yaml':
      case 'sql':
      case 'sh':
      case 'bat':
      case 'c':
      case 'cpp':
      case 'h':
      case 'hpp':
      case 'java':
      case 'kt':
      case 'swift':
      case 'php':
      case 'rb':
      case 'py':
      case 'go':
      case 'rs':
      case 'pl':
      case 'lua':
      case 'r':
      case 'm':
      case 'asm':
      case 'vue':
      case 'svelte':
      case 'toml':
      case 'ini':
      case 'cfg':
        return FileCodeIcon;

      default:
        return FileIcon;
    }
  }, [extension]);

  return <IconComponent size={SVG_SIZE_L} />;
};
