'use client';

import { SuperNinjaIcon } from '@/assets/icons/superNinjaIcon';
import styles from './NinjaLogo.module.scss';
import { SVG_SIZE_M } from '@/constants';
import classNames from 'classnames';

interface NinjaLogoProps {
  size?: number;
  className?: string;
}

export const NinjaLogo = ({ size = SVG_SIZE_M, className }: NinjaLogoProps) => {
  return (
    <div
      className={classNames(styles.root, className)}
      style={{ width: size, height: size }}
    >
      <SuperNinjaIcon size={size} />
    </div>
  );
};
