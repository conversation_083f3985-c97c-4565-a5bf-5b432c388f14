'use client';

import styles from './NinjaLoader.module.scss';
import { SuperNinjaIcon } from '@/assets/icons/superNinjaIcon';

interface NinjaLoaderProps {
  content?: string;
}

const SIZE = 160;

export const NinjaLoader = ({ content }: NinjaLoaderProps) => {
  return (
    <div className={styles.root}>
      <div className={styles.imageContainer}>
        <SuperNinjaIcon size={SIZE} />

        <div className={styles.spinner}></div>
      </div>
      {content && <div className={styles.content}>{content}</div>}
    </div>
  );
};
