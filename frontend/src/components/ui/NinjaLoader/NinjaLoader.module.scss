@use '@styles/index' as *;

.root {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 20px;
  text-align: center;
  box-sizing: border-box;
  gap: 36px;
  z-index: 1;
  background-color: var(--nj-background-body);
  min-height: inherit;

  svg {
    position: relative;
    text-align: center;
    border-radius: 50%;
    width: 160px;
    height: 160px;
    z-index: 2;
  }
}

.imageContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 184px;
  height: 184px;
}

.spinner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-color: var(--nj-accent-special) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0);
  border-style: solid;
  border-top-color: var(--nj-accent-special);
  border-width: 4px;
  box-sizing: border-box;
  display: block;
  border-radius: 50%;
  animation: infinite-spinner var(--nj-speed--5x) linear infinite;
  z-index: 1;
}

.content {
  width: 100%;
  color: var(--nj-foreground-secondary);
  text-align: center;
  font-size: 20px;
  font-weight: 600;
  line-height: 28px;

  @include from(tablet) {
    width: calc(100% / 2);
  }
}
