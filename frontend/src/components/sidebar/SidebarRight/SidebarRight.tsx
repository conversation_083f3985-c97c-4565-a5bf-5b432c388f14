'use client';

import styles from './SidebarRight.module.scss';
import { MyNinjaButton } from '@/components/ui/MyNinjaButton';
import { ToggleComputerPanelButton } from '@/components/sidebar/ToggleComputerPanelButton';
import { useSidebar } from '@/components/ui/sidebar';
import { usePathname } from 'next/navigation';

export const SidebarRight = () => {
  const { isComputerButtonExist } = useSidebar();
  const pathname = usePathname();
  const isAgentRoute = pathname.startsWith('/agents/');

  return (
    <div className={styles.root}>
      <div className={styles.topItemsWrapper}>
        <MyNinjaButton />
        {isComputerButtonExist && isAgentRoute && <ToggleComputerPanelButton />}
      </div>
      <div>{/*todo add support button*/}</div>
    </div>
  );
};
