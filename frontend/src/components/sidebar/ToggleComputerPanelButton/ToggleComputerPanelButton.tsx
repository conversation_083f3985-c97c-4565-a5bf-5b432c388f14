'use client';

import { Button } from '@/components/ui/Button/index';
import { Monitor } from '@phosphor-icons/react';
import { SVG_SIZE_M } from '@/constants';
import { useSidebar } from '@/components/ui/sidebar';
import { useCallback } from 'react';

export const ToggleComputerPanelButton = () => {
  const {
    isComputerOpened,
    setIsComputerOpened,
    setOpen: setLeftSidebarOpen,
  } = useSidebar();

  const toggleSidePanel = useCallback(() => {
    setIsComputerOpened((prevIsOpen) => {
      const newState = !prevIsOpen;

      if (newState) {
        // Close left sidebar when opening side panel
        setLeftSidebarOpen(false);
      }
      return newState;
    });
  }, [setLeftSidebarOpen, setIsComputerOpened]);

  const handleClick = () => {
    toggleSidePanel();
  };

  return (
    <Button
      color={isComputerOpened ? 'active-transparent' : 'transparent'}
      shape="round"
      onClick={handleClick}
    >
      <Monitor size={SVG_SIZE_M} />
    </Button>
  );
};
