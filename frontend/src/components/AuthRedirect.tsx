'use client';

import { useAuth } from '@/components/AuthProvider';
import { redirect } from 'next/navigation';
import { NEXT_PUBLIC_MY_NINJA_URL } from '@/lib/envs';

export const AuthRedirect = () => {
  const auth = useAuth();
  if (auth.isLoading === false) {
    if (auth.user) {
      redirect('/dashboard');
    } else {
      redirect(`${NEXT_PUBLIC_MY_NINJA_URL}/login`);
    }
  }
  return null;
};
