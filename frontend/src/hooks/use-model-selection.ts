'use client';

import { useCallback, useEffect, useState } from 'react';
import { DEFAULT_MODEL_ID, STORAGE_KEY_MODEL } from '@/constants';
import { safeSetItem } from '@/lib/utils/safe-local-storage';

export const useModelSelection = () => {
  const [selectedModel, setSelectedModel] = useState<string>(() => {
    if (typeof window === 'undefined') {
      return DEFAULT_MODEL_ID;
    }
    return localStorage.getItem(STORAGE_KEY_MODEL) ?? DEFAULT_MODEL_ID;
  });

  const onModelChange = useCallback((value: string) => {
    setSelectedModel(value);
    safeSetItem(STORAGE_KEY_MODEL, value);
  }, []);

  useEffect(() => {
    const handleStorage = (e: StorageEvent) => {
      if (e.key === STORAGE_KEY_MODEL && typeof e.newValue === 'string') {
        setSelectedModel(e.newValue);
      }
    };
    window.addEventListener('storage', handleStorage);
    return () => window.removeEventListener('storage', handleStorage);
  }, []);

  return {
    selectedModel,
    onModelChange,
  };
};
