'use client';

import { useEffect, useState } from 'react';
import {
  osName,
  osVersion,
  browserName,
  browserVersion,
  getUA,
  isMobile,
  isTablet,
  isDesktop,
  isAndroid,
  isIOS,
  isChrome,
  isFirefox,
  isSafari,
  isEdge,
  isMacOs,
  isWindows,
  isSmartTV,
  isWearable,
  isConsole,
  isMobileOnly,
} from 'react-device-detect';

interface DeviceInfo {
  userAgent: string;
  osName: string;
  osVersion: string;
  browserName: string;
  browserVersion: string;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isAndroid: boolean;
  isIOS: boolean;
  isMacOs: boolean;
  isWindows: boolean;
  isChrome: boolean;
  isFirefox: boolean;
  isSafari: boolean;
  isEdge: boolean;
  isSmartTV: boolean;
  isWearable: boolean;
  isConsole: boolean;
  isMobileOnly: boolean;
}

export const useDeviceDetect = (): DeviceInfo | null => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>();

  useEffect(() => {
    setDeviceInfo({
      userAgent: getUA,
      osName,
      osVersion,
      browserName,
      browserVersion,
      isMobile,
      isTablet,
      isDesktop,
      isAndroid,
      isIOS,
      isMacOs,
      isWindows,
      isChrome,
      isFirefox,
      isSafari,
      isEdge,
      isSmartTV,
      isWearable,
      isConsole,
      isMobileOnly,
    });
  }, []);

  return deviceInfo;
};
