import { UnifiedMessage } from '@/components/thread/types';
import { useCallback, useMemo } from 'react';
import { toast } from 'sonner';

interface UseStreamCallbacksParams {
  threadId: string;
  agentRunId: string;
  agentStatus: 'error' | 'idle' | 'running' | 'connecting';
  setMessages: (updater: (prev: UnifiedMessage[]) => UnifiedMessage[]) => void;
  setAutoOpenedPanel: (value: boolean) => void;
}

export const useCommonStreamCallbacks = ({
  threadId,
  agentRunId,
  setMessages,
  agentStatus,
  setAutoOpenedPanel,
}: UseStreamCallbacksParams) => {
  const handleNewMessageFromStream = useCallback((message: UnifiedMessage) => {
    // Log the ID of the message received from the stream
    console.log(
      `[STREAM HANDLER] Received message: ID=${message.message_id}, Type=${message.type}`,
    );
    if (!message.message_id) {
      console.warn(
        `[STREAM HANDLER] Received message is missing ID: Type=${message.type}, Content=${message.content?.substring(0, 50)}...`,
      );
    }

    setMessages((prev) => {
      // First check if the message already exists
      const messageExists = prev.some(
        (m) => m.message_id === message.message_id,
      );
      if (messageExists) {
        // If it exists, update it instead of adding a new one
        return prev.map((m) =>
          m.message_id === message.message_id ? message : m,
        );
      } else {
        // If it's a new message, add it to the end
        return [...prev, message];
      }
    });

    // If we received a tool message, refresh the tool panel
    if (message.type === 'tool') {
      setAutoOpenedPanel(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleNewErrorNotificationFromStream = useCallback(
    (errorNotification: string) => {
      // Log the error notification received from the stream
      console.log(
        `[STREAM HANDLER] Received error-notification: ${errorNotification}`,
      );

      const optimisticAssistantMessage: UnifiedMessage = {
        message_id: `temp-${Date.now()}`,
        thread_id: threadId,
        type: 'assistant',
        is_llm_message: true,
        content: `{\"role\": \"assistant\", \"content\": \"${errorNotification}\", \"tool_calls\": null}`,
        metadata: `{\"thread_run_id\": \"${agentRunId}\"}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      setMessages((prev) => {
        return [...prev, optimisticAssistantMessage];
      });

      setAutoOpenedPanel(false);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [threadId, agentRunId],
  );

  const handleStreamError = useCallback((errorMessage: string) => {
    console.error(`[PAGE] Stream hook error: ${errorMessage}`);
    if (
      !errorMessage.toLowerCase().includes('not found') &&
      !errorMessage.toLowerCase().includes('agent run is not running')
    ) {
      toast.error(`Stream Error: ${errorMessage}`);
    }
  }, []);

  const handleStreamClose = useCallback(() => {
    console.log(`[PAGE] Stream hook closed with final status: ${agentStatus}`);
  }, [agentStatus]);

  return useMemo(
    () => ({
      setAutoOpenedPanel,
      handleStreamError,
      handleStreamClose,
      handleNewMessageFromStream,
      handleNewErrorNotificationFromStream,
    }),
    [
      setAutoOpenedPanel,
      handleStreamError,
      handleStreamClose,
      handleNewMessageFromStream,
      handleNewErrorNotificationFromStream,
    ],
  );
};
