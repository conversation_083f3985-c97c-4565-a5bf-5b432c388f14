// utils/amplify-utils.ts
import { cookies } from 'next/headers';

import { createServerRunner } from '@aws-amplify/adapter-nextjs';
import { generateServerClientUsingCookies } from '@aws-amplify/adapter-nextjs/api';
import { fetchAuthSession, getCurrentUser } from 'aws-amplify/auth/server';

import { getAmplifyConfig } from './amplify-config';
import { ResourcesConfig } from 'aws-amplify';

export const { runWithAmplifyServerContext } = createServerRunner({
  config: getAmplifyConfig() as ResourcesConfig,
});

export const cookiesClient = generateServerClientUsingCookies({
  config: getAmplifyConfig() as ResourcesConfig,
  cookies,
});

export async function AuthGetCurrentUserServer() {
  try {
    const currentUser = await runWithAmplifyServerContext({
      nextServerContext: { cookies },
      operation: (contextSpec) => getCurrentUser(contextSpec),
    });
    return currentUser;
  } catch (error) {
    console.error(error);
  }
}
export async function AuthGetAuthSessionServer() {
  try {
    const currentSession = await runWithAmplifyServerContext({
      nextServerContext: { cookies },
      operation: (contextSpec) => fetchAuthSession(contextSpec),
    });
    return currentSession;
  } catch (error) {
    console.error(error);
  }
}
