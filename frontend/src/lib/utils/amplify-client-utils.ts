'use client';

import { fetchAuthSession } from '@aws-amplify/core';

export const getUserFromAmplifySession = async () => {
  try {
    const currentSession = await fetchAuthSession();
    return {
      ninja_user_id:
        (currentSession?.tokens?.accessToken?.payload[
          'custom:ninja_user_id'
        ] as string) || '',
      id: (currentSession?.tokens?.accessToken?.payload?.sub as string) || '',
      name:
        (currentSession?.tokens?.idToken?.payload['given_name'] as string) ||
        'User',
      email:
        (currentSession?.tokens?.idToken?.payload['email'] as string) || '',
      avatar: '',
    };
  } catch (error) {
    console.error('Error fetching user from Amplify session:', error);
    return null;
  }
};
