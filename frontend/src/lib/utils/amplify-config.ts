import {
  NEXT_PUBLIC_COGNITO_USER_POOL_ID,
  NEXT_PUBLIC_COGNITO_USER_POOL_CLIENT_ID,
  NEXT_PUBLIC_COGNITO_OAUTH_DOMAIN,
  NEXT_PUBLIC_URL,
} from '@/lib/envs';

const amplifyConfig = {
  Auth: {
    Cognito: {
      userPoolId: NEXT_PUBLIC_COGNITO_USER_POOL_ID,
      userPoolClientId: NEXT_PUBLIC_COGNITO_USER_POOL_CLIENT_ID,
      loginWith: {
        oauth: {
          domain: NEXT_PUBLIC_COGNITO_OAUTH_DOMAIN,
          scopes: [
            'profile',
            'email',
            'openid',
            'aws.cognito.signin.user.admin',
          ],
          redirectSignIn: [NEXT_PUBLIC_URL + '/login'],
          redirectSignOut: [NEXT_PUBLIC_URL + '/logout'],
          responseType: 'code', // or 'token', note that REFRESH token will only be generated when the responseType is code
        },
      },
    },
  },
};

export const getAmplifyConfig = () => amplifyConfig;
