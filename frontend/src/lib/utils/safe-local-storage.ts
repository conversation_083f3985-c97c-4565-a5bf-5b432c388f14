const isBrowser = typeof window !== 'undefined';

export const safeSetItem = (key: string, value: string): void => {
  if (!isBrowser) {
    return;
  }

  try {
    window.localStorage.setItem(key, value);
  } catch (error) {
    if (process.env.NODE_ENV !== 'production') {
      console.warn(`[safeSetItem] failed for key "${key}"`, error);
    }
  }
};

export const safeGetItem = (key: string): string | null => {
  if (!isBrowser) {
    return null;
  }

  try {
    return window.localStorage.getItem(key);
  } catch (error) {
    if (process.env.NODE_ENV !== 'production') {
      console.warn(`[safeGetItem] failed for key "${key}"`, error);
    }
    return null;
  }
};

export const safeRemoveItem = (key: string): void => {
  if (!isBrowser) {
    return;
  }

  try {
    window.localStorage.removeItem(key);
  } catch (error) {
    if (process.env.NODE_ENV !== 'production') {
      console.warn(`[safeRemoveItem] failed for key "${key}"`, error);
    }
  }
};
