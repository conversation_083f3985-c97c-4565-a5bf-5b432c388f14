import { safeJsonParse } from '@/components/thread/utils';
import {
  ArrowSquareOutIcon,
  ChatIcon,
  CloudArrowUpIcon,
  CodeIcon,
  FileDashedIcon,
  FileMagnifyingGlassIcon,
  FilePlusIcon,
  FileTextIcon,
  FileXIcon,
  GearFineIcon,
  GlobeIcon,
  Icon,
  MagnifyingGlassIcon,
  NetworkIcon,
  PencilSimpleLineIcon,
  TerminalIcon,
  WrenchIcon,
} from '@phosphor-icons/react';

export const extractWebsiteUrl = (rawJson: string): string | null => {
  const tryParse = (data: unknown): string | null => {
    if (typeof data === 'string') {
      const m = data.match(/"website-url"\s*:\s*"([^"]+)"/);
      if (m) return m[1];

      try {
        return tryParse(safeJsonParse(data, ''));
      } catch {
        return null;
      }
    }

    if (data && typeof data === 'object') {
      if ('website-url' in (data as Record<string, unknown>)) {
        return (data as Record<string, unknown>)['website-url'] as string;
      }

      return (
        Object.values(data)
          .map(tryParse)
          .find((url) => url !== null) ?? null
      );
    }

    return null;
  };

  return tryParse(rawJson);
};

export const getToolIcon = (toolName: string): Icon => {
  if (!toolName) {
    return GearFineIcon;
  }

  const normalizedName = toolName.toLowerCase();

  if (normalizedName.startsWith('browser-')) {
    return GlobeIcon;
  }

  switch (normalizedName) {
    case 'create-file':
      return PencilSimpleLineIcon;
    case 'str-replace':
      return FileMagnifyingGlassIcon;
    case 'full-file-rewrite':
      return FilePlusIcon;
    case 'read-file':
      return FileTextIcon;

    case 'execute-command':
      return TerminalIcon;

    case 'web-search':
      return MagnifyingGlassIcon;
    case 'crawl-webpage':
      return GlobeIcon;

    case 'call-data-provider':
      return ArrowSquareOutIcon;
    case 'get-data-provider-endpoints':
      return NetworkIcon;
    case 'execute-data-provider-call':
      return NetworkIcon;

    case 'delete-file':
      return FileXIcon;

    case 'deploy-site':
      return CloudArrowUpIcon;

    case 'execute-code':
      return CodeIcon;

    case 'ask':
      return ChatIcon;

    default:
      return WrenchIcon;
  }
};
