import { env } from 'next-runtime-env';

export const NEXT_PUBLIC_ENV_MODE =
  env('NEXT_PUBLIC_ENV_MODE')?.toLowerCase() || 'local';
export const NEXT_PUBLIC_SUPABASE_URL = env('NEXT_PUBLIC_SUPABASE_URL');
export const NEXT_PUBLIC_SUPABASE_ANON_KEY = env(
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
);
export const NEXT_PUBLIC_BACKEND_URL = env('NEXT_PUBLIC_BACKEND_URL');
export const NEXT_PUBLIC_URL = env('NEXT_PUBLIC_URL');
export const NEXT_PUBLIC_GOOGLE_CLIENT_ID = env('NEXT_PUBLIC_GOOGLE_CLIENT_ID');

export const NEXT_PUBLIC_COGNITO_USER_POOL_ID = env(
  'NEXT_PUBLIC_COGNITO_USER_POOL_ID',
);
export const NEXT_PUBLIC_COGNITO_USER_POOL_CLIENT_ID = env(
  'NEXT_PUBLIC_COGNITO_USER_POOL_CLIENT_ID',
);
export const NEXT_PUBLIC_COGNITO_OAUTH_DOMAIN = env(
  'NEXT_PUBLIC_COGNITO_OAUTH_DOMAIN',
);
export const NEXT_PUBLIC_COGNITO_COOKIES_DOMAIN =
  env('NEXT_PUBLIC_COGNITO_COOKIES_DOMAIN') || 'localhost';
export const NEXT_PUBLIC_MY_NINJA_URL = env('NEXT_PUBLIC_MY_NINJA_URL');
