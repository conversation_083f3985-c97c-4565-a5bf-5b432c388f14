'use server';

import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { type SupabaseClient } from '@supabase/supabase-js';
import { cache } from 'react';
import {
  NEXT_PUBLIC_SUPABASE_ANON_KEY,
  NEXT_PUBLIC_SUPABASE_URL,
} from '@/lib/envs';

import { AuthGetAuthSessionServer } from '@/lib/utils/amplify-utils';

// it should be renamed to createServerSupabase
export const createClient = cache(async (): Promise<SupabaseClient> => {
  const cookieStore = await cookies();

  const supabaseUrl = NEXT_PUBLIC_SUPABASE_URL.startsWith('http')
    ? NEXT_PUBLIC_SUPABASE_URL
    : `https://${NEXT_PUBLIC_SUPABASE_URL}`;

  const supabase = createServerClient(
    supabaseUrl,
    NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      accessToken: async () => {
        const { tokens } = await AuthGetAuthSessionServer();
        return tokens?.accessToken.toString() || null;
      },
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set({ name, value, ...options }),
            );
          } catch (error) {
            // The `set` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    },
  );
  return supabase;
});
