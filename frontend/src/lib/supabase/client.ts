'use client';

import { createBrowserClient } from '@supabase/ssr';
import { fetchAuthSession } from '@aws-amplify/core';
import {
  NEXT_PUBLIC_SUPABASE_URL,
  NEXT_PUBLIC_SUPABASE_ANON_KEY,
} from '@/lib/envs';
export const createClient = () => {
  // Get URL and key from environment variables
  let supabaseUrl = NEXT_PUBLIC_SUPABASE_URL;

  // Ensure the URL is in the proper format with http/https protocol
  if (supabaseUrl && !supabaseUrl.startsWith('http')) {
    // If it's just a hostname without protocol, add http://
    supabaseUrl = `http://${supabaseUrl}`;
  }

  return createBrowserClient(supabaseUrl, NEXT_PUBLIC_SUPABASE_ANON_KEY, {
    accessToken: async () => {
      const currentSession = await fetchAuthSession();
      // Alternatively you can use tokens?.idToken instead.
      return currentSession?.tokens?.accessToken.toString() || null;
    },
  });
};
