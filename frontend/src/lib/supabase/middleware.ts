import { type NextRequest, NextResponse } from 'next/server';
import { fetchAuthSession } from 'aws-amplify/auth/server';
import { runWithAmplifyServerContext } from '@/lib/utils/amplify-utils';

function forceLoginWithReturn(request: NextRequest) {
  const originalUrl = new URL(request.url);
  const path = originalUrl.pathname;
  const query = originalUrl.searchParams.toString();
  return NextResponse.redirect(
    new URL(
      `/auth?returnUrl=${encodeURIComponent(path + (query ? `?${query}` : ''))}`,
      request.url,
    ),
  );
}

export const validateSession = async (request: NextRequest) => {
  // This `try/catch` block is only here for the interactive tutorial.
  // Feel free to remove once you have Supabase connected.
  try {
    // Create an unmodified response
    const response = NextResponse.next({
      request: {
        headers: request.headers,
      },
    });
    const authenticated = await runWithAmplifyServerContext({
      nextServerContext: { request, response },
      operation: async (contextSpec) => {
        try {
          const session = await fetchAuthSession(contextSpec, {});
          return session.tokens !== undefined;
        } catch (error) {
          console.log(error);
          return false;
        }
      },
    });
    const protectedRoutes = ['/dashboard', '/invitation'];

    if (
      !authenticated &&
      protectedRoutes.some((path) => request.nextUrl.pathname.startsWith(path))
    ) {
      // redirect to /auth
      return forceLoginWithReturn(request);
    }

    return response;
  } catch (e) {
    console.error('Error validating session:', e);
    return NextResponse.next({
      request: {
        headers: request.headers,
      },
    });
  }
};
