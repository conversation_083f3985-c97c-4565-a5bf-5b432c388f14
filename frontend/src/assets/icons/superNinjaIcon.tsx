'use client';

import { SVGProps } from 'react';

import { SVG_SIZE_M } from '@/constants';

interface NinjaLogoProps extends SVGProps<SVGSVGElement> {
  size?: number;
}

export const SuperNinjaIcon = ({
  size = SVG_SIZE_M,
  ...svgProps
}: NinjaLogoProps) => (
  <svg
    {...svgProps}
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 40 40"
    fill="none"
  >
    <g clipPath="url(#clip0_22_2821)">
      <path
        d="M19.9992 10.0007H20C15.8414 10.0007 12.1289 11.1647 9.68437 13.4937C9.57421 13.3499 9.48515 13.2272 9.42734 13.1343C9.05859 12.5468 8.62812 11.8577 8.31406 11.1624C8.25624 11.0335 8.20546 10.9124 8.16171 10.7983C8.43203 10.8647 8.75468 10.9124 9.10312 10.8897C9.43437 10.8687 9.7914 10.7546 10.1094 10.5155C10.4133 10.2874 10.5914 10.0132 10.6937 9.79365C10.8805 9.39365 10.9 8.99678 10.8977 8.75537C10.8937 8.22959 10.768 7.61475 10.6242 7.05615C10.325 5.8999 9.73124 1.71318 9.36952 0.833496L6.72656 4.90771C7.02421 5.63037 7.40468 6.65693 7.66874 7.571C7.60703 7.5585 7.54218 7.54834 7.47578 7.53975C7.23046 7.50928 6.8664 7.50146 6.46249 7.64756C5.4789 8.00381 5.15703 8.8749 5.05781 9.41318C4.8539 10.5257 5.23593 11.6468 5.60781 12.471C5.99687 13.3343 6.38593 13.6663 6.38593 13.6663L7.45703 14.9022L8.1039 15.4788C7.5914 16.3296 7.19609 17.2733 6.95624 18.3233C6.40937 18.0437 5.77031 17.7163 5.20937 17.3468C5.10546 17.278 5.01015 17.2124 4.92343 17.1491C5.14453 17.1069 5.39218 17.0351 5.62343 16.9077C5.84296 16.7866 6.03515 16.5944 6.13828 16.3296C6.23671 16.0772 6.22499 15.8335 6.18749 15.6515C6.11952 15.3202 5.93671 15.0444 5.81562 14.8812C5.55312 14.5257 5.16171 14.1491 4.78437 13.8171C4.00468 13.1296 1.52109 10.4812 0.833588 10.0007L1.01015 13.6171C1.57421 14.0116 2.34687 14.5843 2.98281 15.1194C2.93359 15.1312 2.88359 15.1444 2.83281 15.1608C2.64765 15.2194 2.38984 15.3319 2.18124 15.5608C1.67343 16.1194 1.88046 16.814 2.07734 17.2108C2.48515 18.0312 3.30546 18.6687 3.97187 19.1077C4.66953 19.5679 5.10468 19.6679 5.10468 19.6679L6.46093 20.1608L6.7039 20.221C6.68984 20.4647 6.66562 20.7015 6.66562 20.9538C6.66562 28.2585 12.6352 35.8335 19.9992 35.8335C27.3633 35.8335 33.3328 28.2585 33.3328 20.9538C33.3328 13.6491 27.3633 10.0007 19.9992 10.0007ZM10.7328 29.5835C10.7328 29.5835 10.7289 29.578 10.7273 29.5757C8.72578 27.0851 7.50078 23.8796 7.50078 20.7288C7.50078 14.1296 12.875 10.8335 19.5047 10.8335C24.0187 10.8335 27.9508 12.3624 30.0008 15.421C27.8414 12.8601 24.1976 11.5804 20.0672 11.5804C13.4375 11.5804 8.06328 14.8765 8.06328 21.4757C8.06328 24.3194 9.06093 27.2085 10.7273 29.5757C10.7297 29.578 10.7312 29.5812 10.7336 29.5835H10.7328ZM20 30.8327C14.3844 30.8327 8.95859 26.0858 8.95859 21.3132C8.95859 16.5405 14.3844 17.5999 20 17.5999C25.6156 17.5999 31.0414 16.5405 31.0414 21.3132C31.0414 26.0858 25.6156 30.8327 20 30.8327Z"
        fill="url(#paint0_linear_22_2821)"
      />
      <path
        d="M29.0344 21.6001C29.5023 22.7915 28.5195 24.2118 26.8383 24.7728C25.1578 25.3329 23.4156 24.822 22.9477 23.6298C22.4797 22.4384 23.4211 21.9454 25.1023 21.3845C26.7828 20.8235 28.5664 20.4079 29.0344 21.6001ZM10.9656 21.6001C10.4977 22.7915 11.4805 24.2118 13.1617 24.7728C14.8422 25.3329 16.5844 24.822 17.0523 23.6298C17.5203 22.4384 16.5789 21.9454 14.8977 21.3845C13.2172 20.8235 11.4336 20.4079 10.9656 21.6001Z"
        fill="url(#paint1_linear_22_2821)"
      />
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_22_2821"
        x1="0.833588"
        y1="18.3335"
        x2="33.3328"
        y2="18.3335"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#E96384" />
        <stop offset="1" stopColor="#3D00FF" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_22_2821"
        x1="10.849"
        y1="22.9164"
        x2="29.151"
        y2="22.9164"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#E96384" />
        <stop offset="1" stopColor="#3D00FF" />
      </linearGradient>
      <clipPath id="clip0_22_2821">
        <rect width="40" height="40" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
