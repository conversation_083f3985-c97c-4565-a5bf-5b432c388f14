import os

import aws_cdk as cdk
from aws_cdk import (
    aws_certificatemanager as acm,
    aws_cloudfront as cloudfront,
    aws_cloudfront_origins as origins,
    aws_ec2 as ec2,
    aws_elasticloadbalancingv2 as elbv2,
    aws_lambda,
    aws_route53,
    aws_route53_targets,
    aws_s3 as s3,
)
from aws_cdk.aws_cloudfront import (
    AllowedMethods,
    EdgeLambda,
    LambdaEdgeEventType,
    OriginRequestPolicy,
    ResponseHeadersPolicy,
    ViewerProtocolPolicy,
)
from aws_cdk.aws_elasticache import CfnReplicationGroup, CfnSubnetGroup
from aws_cdk.aws_elasticloadbalancingv2 import (
    ApplicationListener,
    ApplicationProtocol,
    ListenerAction,
    SslPolicy,
)
from aws_cdk.aws_iam import PolicyStatement
from aws_cdk.aws_lambda import Runtime
from constructs import Construct
from ninja_common.constants import ALB_IDLE_TIMEOUT, ALB_KEEP_ALIVE_TIMEOUT
from ninja_common.stages.config import StageConfig
from ninja_common_infra.constructs import NinjaStack

from infra.constants import CLOUDFRONT_IP_LIST
from infra.lambdas.shared.utils.constants import UI_AUTH_EDGE_LAMBDA_PASSWORD_SECRET_NAME
from infra.utils.utils import get_super_agent_ui_domain, get_super_agent_website_domain


class CoreStack(NinjaStack):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        stage_config: StageConfig,
        **kwargs,
    ):
        super().__init__(scope, construct_id, stage_config=stage_config, **kwargs)

        subnet_group = CfnSubnetGroup(
            self,
            id=f"SuperAgentRedisSubnetGroup-{stage_config.stage_name}",
            description="Subnet group for Redis",
            subnet_ids=[subnet.subnet_id for subnet in self.vpc.private_subnets],
            cache_subnet_group_name=f"SuperAgentRedisSubnetGroup-{stage_config.stage_name}",
        )

        # Create a security group for the Redis cluster
        redis_security_group = ec2.SecurityGroup(
            self,
            "SuperAgentRedisSecurityGroup",
            vpc=self.vpc,
            description="Security group for SuperAgent Redis cluster",
            allow_all_outbound=True,
        )

        # Allow incoming traffic from within the VPC on port 6379
        redis_security_group.add_ingress_rule(
            ec2.Peer.ipv4(self.vpc.vpc_cidr_block),
            ec2.Port.tcp(6379),
            "Allow incoming Redis traffic from the VPC",
        )

        # Create the Redis replication group
        redis_replication_group = CfnReplicationGroup(
            self,
            "SuperAgentRedisReplicationGroup",
            engine="Redis",
            engine_version="7.0",
            replication_group_description="Redis replication group for the Super Agent usage",
            cache_node_type="cache.t4g.small",
            automatic_failover_enabled=True,
            replicas_per_node_group=2,
            num_node_groups=1,
            cache_subnet_group_name=subnet_group.cache_subnet_group_name,
            security_group_ids=[redis_security_group.security_group_id],
        )

        redis_replication_group.add_depends_on(subnet_group)

        self.redis_address = redis_replication_group.attr_primary_end_point_address
        self.redis_port = redis_replication_group.attr_primary_end_point_port
        cdk.CfnOutput(self, "RedisAddress", value=self.redis_address)
        cdk.CfnOutput(self, "RedisPort", value=self.redis_port)

        self.ui_stage_zone = aws_route53.PublicHostedZone.from_hosted_zone_attributes(
            self,
            "UiStageHostZone",
            zone_name=self.stage_config.ui_zone_name,
            hosted_zone_id=self.stage_config.ui_zone_id or os.environ["DNS_UI_ZONE_ID"],
        )

        self._add_website_deployment_resources()
        self._add_private_lb()

    def _add_private_lb(self):
        self.private_alb_sg = ec2.SecurityGroup(
            self,
            "PrivateLbSg",
            vpc=self.vpc,
            description="Private SuperAgent Load Balancer SG",
            allow_all_outbound=False,
        )
        self.private_alb_sg.add_egress_rule(
            ec2.Peer.any_ipv4(), ec2.Port.tcp(80), "Allow outbound access on 80 port"
        )
        # allow access to LB only from CloudFront IPs
        self.private_alb_sg.add_ingress_rule(
            ec2.Peer.prefix_list(CLOUDFRONT_IP_LIST),
            ec2.Port.tcp(443),
            "Allow inbound access to LB on 443 port from CloudFront",
        )

        self.private_alb = elbv2.ApplicationLoadBalancer(
            self,
            "PrivateLB",
            vpc=self.vpc,
            security_group=self.private_alb_sg,
            internet_facing=False,
            idle_timeout=cdk.Duration.seconds(ALB_IDLE_TIMEOUT),
            client_keep_alive=cdk.Duration.seconds(ALB_KEEP_ALIVE_TIMEOUT),
        )
        self.private_alb.add_redirect(open=False)

        self.private_alb_cert_us_west_2 = acm.DnsValidatedCertificate(
            self,
            "SslCertWest",
            hosted_zone=self.ui_stage_zone,
            domain_name=f"*.{self.stage_config.ui_zone_name}",
            region="us-west-2",
        )

        self.public_alb_cert_us_east_1 = acm.DnsValidatedCertificate(
            self,
            "RootCertEast",
            hosted_zone=self.ui_stage_zone,
            domain_name=f"*.{self.stage_config.ui_zone_name}",
            region="us-east-1",
        )

        self.private_alb_listener: ApplicationListener = ApplicationListener(
            self,
            "ALBListener",
            load_balancer=self.private_alb,
            certificates=[self.private_alb_cert_us_west_2],
            default_action=ListenerAction.fixed_response(status_code=401),
            port=443,
            open=False,
            ssl_policy=SslPolicy.TLS12,
            protocol=ApplicationProtocol.HTTPS,
        )

        edge_lambdas = []
        if not self.stage_config.contains_customer_data:
            edge_lambdas.append(self._fe_distribution_auth_lambda())

        self.fe_distribution = cloudfront.Distribution(
            self,
            "FEDistribution",
            default_behavior=cloudfront.BehaviorOptions(
                origin=origins.VpcOrigin.with_application_load_balancer(
                    alb=self.private_alb,
                ),
                edge_lambdas=edge_lambdas,
                allowed_methods=AllowedMethods.ALLOW_ALL,
                origin_request_policy=OriginRequestPolicy.ALL_VIEWER_AND_CLOUDFRONT_2022,
                response_headers_policy=ResponseHeadersPolicy.CORS_ALLOW_ALL_ORIGINS,
                viewer_protocol_policy=ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
                cache_policy=cloudfront.CachePolicy.USE_ORIGIN_CACHE_CONTROL_HEADERS_QUERY_STRINGS,
            ),
            certificate=self.public_alb_cert_us_east_1,
            domain_names=[get_super_agent_ui_domain(self.stage_config)],
        )

        aws_route53.ARecord(
            self,
            "UiDnsRecord",
            record_name=get_super_agent_ui_domain(self.stage_config),
            target=aws_route53.RecordTarget.from_alias(
                aws_route53_targets.CloudFrontTarget(self.fe_distribution)
            ),
            zone=self.ui_stage_zone,
        )

        # keep CFN happy if we remove alb dependency
        self.export_value(self.private_alb_listener.listener_arn)
        self.export_value(self.private_alb_sg.security_group_id)

    def _fe_distribution_auth_lambda(self):
        request_viewer_edge = cloudfront.experimental.EdgeFunction(
            self,
            "RedirectViewerRequestEdge",
            handler="frontend.request_viewer.handler.handler",
            # the only way to pass env into viewer lambdas is to make the env the part of the function name
            function_name=f"superagent-fe-cfn-viewer-request-{self.stage_config.stage_name}",
            timeout=cdk.Duration.seconds(5),
            runtime=Runtime.PYTHON_3_12,
            code=aws_lambda.Code.from_asset(f"{self.service_root_path}/infra/lambdas"),
        )

        request_viewer_edge.add_to_role_policy(
            PolicyStatement(
                actions=["secretsmanager:DescribeSecret", "secretsmanager:GetSecretValue"],
                resources=[
                    f"arn:aws:secretsmanager:{self.stage_config.aws_region}:{self.stage_config.aws_account_id}:secret:{UI_AUTH_EDGE_LAMBDA_PASSWORD_SECRET_NAME}-*"
                ],
            )
        )
        request_viewer_edge.add_to_role_policy(
            PolicyStatement(
                actions=["kms:Verify"],
                resources=[
                    "*"  # TODO: Stan - for some reason I got access denied.. feel free to fix this if you know what's wrong in this arn.
                    # f"arn:aws:kms:{self.stage_config.aws_region}:{self.stage_config.aws_account_id}:alias/{UI_AUTH_EDGE_LAMBDA_KMS_ALIAS}"
                ],
            )
        )

        return EdgeLambda(
            event_type=LambdaEdgeEventType.VIEWER_REQUEST,
            function_version=request_viewer_edge.current_version,
        )

    def _add_website_deployment_resources(self):
        # Create S3 bucket to store the deployed websites
        website_bucket_suffix = (
            f"dev-{os.environ['DEV_DNS_NAME']}"
            if self.stage_config.is_dev()
            else self.stage_config.stage_name
        )
        website_bucket = s3.Bucket(
            self,
            "SuperAgentWebsiteBucket",
            # Do we want bucket versioning?
            bucket_name=f"ninja-super-agent-websites-{website_bucket_suffix}",
        )
        # Create a certificate for the domain name for cloudfront
        website_domain = get_super_agent_website_domain(self.stage_config)
        website_certificate = acm.DnsValidatedCertificate(
            self,
            "SuperAgentWebsiteCertificate",
            hosted_zone=self.ui_stage_zone,
            domain_name=website_domain,
            region="us-east-1",
        )
        # Create CloudFront distribution for website access
        website_distribution = cloudfront.Distribution(
            self,
            "NinjaSuperAgentWebsiteDistribution",
            default_behavior=cloudfront.BehaviorOptions(
                origin=origins.S3BucketOrigin.with_origin_access_control(website_bucket),
                viewer_protocol_policy=cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
                cache_policy=cloudfront.CachePolicy.CACHING_OPTIMIZED,
            ),
            certificate=website_certificate,
            domain_names=[website_domain],
        )
        # Create an A record in route53 for the CNAME of Cloudfront
        website_record = aws_route53.ARecord(
            self,
            "WebsiteDeploymentRecord",
            record_name=website_domain,
            target=aws_route53.RecordTarget.from_alias(
                aws_route53_targets.CloudFrontTarget(website_distribution)
            ),
            zone=self.ui_stage_zone,
        )

        # Add values we can export to container
        self.website_deployment_bucket = website_bucket
        self.website_deployment_distribution = website_distribution
        self.website_deployment_url = website_record.domain_name
