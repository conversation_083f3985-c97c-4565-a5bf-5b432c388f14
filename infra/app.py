from backend_stack import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tack
from core import CoreStack
from frontend_stack import SuperAgent<PERSON>Stack
from ninja_common.stages.config import StageConfig
from ninja_common_infra.constructs import NinjaApp, NinjaServiceStage

from backend.src.utils.constants import SUPER_AGENT_SERVICE_NAME


class NinjaSuperAgentStage(NinjaServiceStage):
    @staticmethod
    def build_stacks(scope, stage_config: StageConfig, service_name: str):
        core_stack = CoreStack(
            scope,
            "SuperAgentCore",
            stage_config=stage_config,
            service_name=service_name,
        )

        SuperAgentApiStack(
            scope,
            "SuperAgentApi",
            stage_config=stage_config,
            core_stack=core_stack,
            service_name=service_name,
        )

        SuperAgentUIStack(
            scope,
            "SuperAgentUIApi",
            stage_config=stage_config,
            core_stack=core_stack,
            service_name=service_name,
        )


app = NinjaApp(
    repository_name="ninja-suna-manus",
    service_name=SUPER_AGENT_SERVICE_NAME,
    service_stage=NinjaSuperAgentStage,
    synth_poetry_dependency_groups=["infra"],
)

app.synth()
