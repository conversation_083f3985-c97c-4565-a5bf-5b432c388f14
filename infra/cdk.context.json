{"vpc-provider:account=************:filter.vpc-id=vpc-06fc56bf73ef46026:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-06fc56bf73ef46026", "vpcCidrBlock": "10.7.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "Public", "type": "Public", "subnets": [{"subnetId": "subnet-01b65899962035ac5", "cidr": "10.7.16.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-07922685bfa69eff6"}, {"subnetId": "subnet-0188d03dddbc111b5", "cidr": "10.7.0.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0130414655c80212a"}, {"subnetId": "subnet-03fcf4bf0a127fb74", "cidr": "10.7.32.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0e11917fb76309e95"}]}, {"name": "Isolated", "type": "Isolated", "subnets": [{"subnetId": "subnet-02f5a9da15203742e", "cidr": "10.7.112.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0a5b3e1db85070c47"}, {"subnetId": "subnet-0bbe2fee87dec5b91", "cidr": "10.7.96.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0bd59af08875042bf"}, {"subnetId": "subnet-0cf8126dc47b73ebd", "cidr": "10.7.128.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-059093d646f6e08bf"}]}, {"name": "Private", "type": "Private", "subnets": [{"subnetId": "subnet-02a31945b6f865282", "cidr": "10.7.64.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-09cb0ec2ba126cdcb"}, {"subnetId": "subnet-0b5c45a4267e14ab3", "cidr": "10.7.48.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-06ca3504a4a0ee75a"}, {"subnetId": "subnet-0cbb7eb62131d973b", "cidr": "10.7.80.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-060fefb8c881ef4a7"}]}]}, "vpc-provider:account=************:filter.tag:Name=Product-beta-VPC:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-03465c8e735225d11", "vpcCidrBlock": "10.3.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "Product-beta-Public-Subnets", "type": "Public", "subnets": [{"subnetId": "subnet-057834e61bf85bbaf", "cidr": "10.3.0.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0b3c416cca1366f0c"}, {"subnetId": "subnet-0db72dec85158fd3a", "cidr": "10.3.16.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-09ebe51b86a7736df"}, {"subnetId": "subnet-0caa6b51200937d90", "cidr": "10.3.32.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-043007403392ec350"}]}, {"name": "Product-beta-Private-Subnets", "type": "Private", "subnets": [{"subnetId": "subnet-045b3b0df291e85d7", "cidr": "10.3.48.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0292674f48556ed71"}, {"subnetId": "subnet-0f009ac95ece31fd0", "cidr": "10.3.64.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-01d89ef385295a729"}, {"subnetId": "subnet-0210dde4a8cd3be7a", "cidr": "10.3.80.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-08cf289d06bbfd63b"}]}, {"name": "Product-beta-Isolated-Subnets", "type": "Isolated", "subnets": [{"subnetId": "subnet-0a32f14c25cd889de", "cidr": "10.3.96.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0b409a391234560cb"}, {"subnetId": "subnet-05380752697b0ae71", "cidr": "10.3.112.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0d23bdd4b67665f72"}, {"subnetId": "subnet-0e75e54095ce6d1c1", "cidr": "10.3.128.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-03cd1742c2f2b76b2"}]}]}, "vpc-provider:account=************:filter.tag:Name=Product-gamma-VPC:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-0e88826de4610bd7c", "vpcCidrBlock": "10.5.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "Product-gamma-Private-Subnets", "type": "Private", "subnets": [{"subnetId": "subnet-0525483f680f26e27", "cidr": "10.5.48.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0c9a682daea8d4388"}, {"subnetId": "subnet-0b77575df068cbc3e", "cidr": "10.5.64.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0cede3c73d65c6c87"}, {"subnetId": "subnet-031b11c86f342b466", "cidr": "10.5.80.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-098f4dd39fd8f144d"}]}, {"name": "Product-gamma-Public-Subnets", "type": "Public", "subnets": [{"subnetId": "subnet-050bd644aeb2da9cb", "cidr": "10.5.0.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-02a6b44b987466058"}, {"subnetId": "subnet-03718f673847eb8fa", "cidr": "10.5.16.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0e95abc1c2cc43441"}, {"subnetId": "subnet-091ce95d9a2e2282a", "cidr": "10.5.32.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-069b58415e9d96b2a"}]}, {"name": "Product-gamma-Isolated-Subnets", "type": "Isolated", "subnets": [{"subnetId": "subnet-06ca34c8df69691a3", "cidr": "10.5.96.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-072b32ade40b9913a"}, {"subnetId": "subnet-0f572842d9bd8d5bb", "cidr": "10.5.112.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0d8e1977a22c7dc6d"}, {"subnetId": "subnet-09d5d3a2cad9e9a7a", "cidr": "10.5.128.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0929e268666802231"}]}]}, "vpc-provider:account=************:filter.tag:Name=Product-prod-VPC:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-039ab0c1159fc0500", "vpcCidrBlock": "10.1.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "Product-prod-Public-Subnets", "type": "Public", "subnets": [{"subnetId": "subnet-0a505036dab398d3b", "cidr": "10.1.0.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-04d14a24d9706b5a1"}, {"subnetId": "subnet-0dc29f26de9f4f0a2", "cidr": "10.1.16.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-069e79656393be847"}, {"subnetId": "subnet-000f4cad13bc7c150", "cidr": "10.1.32.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0798f9b0d014bb63f"}]}, {"name": "Product-prod-Private-Subnets", "type": "Private", "subnets": [{"subnetId": "subnet-0dce640fe9b69a662", "cidr": "10.1.48.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-013c0180e870c15fc"}, {"subnetId": "subnet-0562d3afecee579a2", "cidr": "10.1.64.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-094c71d2143ce67a6"}, {"subnetId": "subnet-08bf7aaa48b504ec2", "cidr": "10.1.80.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0ea90d6d7b3b55123"}]}, {"name": "Product-prod-Isolated-Subnets", "type": "Isolated", "subnets": [{"subnetId": "subnet-047bd0ac3c04d7b53", "cidr": "10.1.96.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-030942f1688438e88"}, {"subnetId": "subnet-0c8a4b799b5d462f7", "cidr": "10.1.112.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0ff1b756c10e01c71"}, {"subnetId": "subnet-06c5b8b73f604e14d", "cidr": "10.1.128.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0b1c7c3aa05627f31"}]}]}, "vpc-provider:account=************:filter.tag:Name=Product-enterprise-VPC:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-0ef066014e0bb17be", "vpcCidrBlock": "10.8.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "Product-enterprise-Public-Subnets", "type": "Public", "subnets": [{"subnetId": "subnet-030d26fad09d82d9a", "cidr": "10.8.0.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-042b829acac17b8c9"}, {"subnetId": "subnet-0b4530a90363dd066", "cidr": "10.8.16.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0f6f2a5736ad92a7f"}, {"subnetId": "subnet-09cecdb3db1fc6352", "cidr": "10.8.32.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0ec067230a37f109a"}]}, {"name": "Product-enterprise-Isolated-Subnets", "type": "Isolated", "subnets": [{"subnetId": "subnet-044bc5e2b62fbdc30", "cidr": "10.8.96.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0be063adf865df887"}, {"subnetId": "subnet-013b2b86fb6f14613", "cidr": "10.8.112.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-062b95026741ad467"}, {"subnetId": "subnet-03ca3b58df467dff7", "cidr": "10.8.128.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-03dfe9429bf048a01"}]}, {"name": "Product-enterprise-Private-Subnets", "type": "Private", "subnets": [{"subnetId": "subnet-0edf74bfe74693bf4", "cidr": "10.8.48.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0847bad698ff54bd3"}, {"subnetId": "subnet-0f82c27b306f2f846", "cidr": "10.8.64.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0c00e5f6a8f808af7"}, {"subnetId": "subnet-0c77f038f42174e3f", "cidr": "10.8.80.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-06d1cb10a1594de1f"}]}]}, "vpc-provider:account=************:filter.vpc-id=vpc-06fc56bf73ef46026:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-06fc56bf73ef46026", "vpcCidrBlock": "10.7.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "Public", "type": "Public", "subnets": [{"subnetId": "subnet-0188d03dddbc111b5", "cidr": "10.7.0.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0130414655c80212a"}, {"subnetId": "subnet-01b65899962035ac5", "cidr": "10.7.16.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-07922685bfa69eff6"}, {"subnetId": "subnet-03fcf4bf0a127fb74", "cidr": "10.7.32.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0e11917fb76309e95"}]}, {"name": "Isolated", "type": "Isolated", "subnets": [{"subnetId": "subnet-0bbe2fee87dec5b91", "cidr": "10.7.96.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0bd59af08875042bf"}, {"subnetId": "subnet-02f5a9da15203742e", "cidr": "10.7.112.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0a5b3e1db85070c47"}, {"subnetId": "subnet-0cf8126dc47b73ebd", "cidr": "10.7.128.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-059093d646f6e08bf"}]}, {"name": "Private", "type": "Private", "subnets": [{"subnetId": "subnet-0b5c45a4267e14ab3", "cidr": "10.7.48.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-06ca3504a4a0ee75a"}, {"subnetId": "subnet-02a31945b6f865282", "cidr": "10.7.64.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-09cb0ec2ba126cdcb"}, {"subnetId": "subnet-0cbb7eb62131d973b", "cidr": "10.7.80.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-060fefb8c881ef4a7"}]}]}, "vpc-provider:account=************:filter.vpc-id=vpc-06fc56bf73ef46026:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-06fc56bf73ef46026", "vpcCidrBlock": "10.7.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "Public", "type": "Public", "subnets": [{"subnetId": "subnet-01b65899962035ac5", "cidr": "10.7.16.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-07922685bfa69eff6"}, {"subnetId": "subnet-0188d03dddbc111b5", "cidr": "10.7.0.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0130414655c80212a"}, {"subnetId": "subnet-03fcf4bf0a127fb74", "cidr": "10.7.32.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0e11917fb76309e95"}]}, {"name": "Isolated", "type": "Isolated", "subnets": [{"subnetId": "subnet-02f5a9da15203742e", "cidr": "10.7.112.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0a5b3e1db85070c47"}, {"subnetId": "subnet-0bbe2fee87dec5b91", "cidr": "10.7.96.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0bd59af08875042bf"}, {"subnetId": "subnet-0cf8126dc47b73ebd", "cidr": "10.7.128.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-059093d646f6e08bf"}]}, {"name": "Private", "type": "Private", "subnets": [{"subnetId": "subnet-02a31945b6f865282", "cidr": "10.7.64.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-09cb0ec2ba126cdcb"}, {"subnetId": "subnet-0b5c45a4267e14ab3", "cidr": "10.7.48.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-06ca3504a4a0ee75a"}, {"subnetId": "subnet-0cbb7eb62131d973b", "cidr": "10.7.80.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-060fefb8c881ef4a7"}]}]}, "vpc-provider:account=************:filter.vpc-id=vpc-06fc56bf73ef46026:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-06fc56bf73ef46026", "vpcCidrBlock": "10.7.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "Public", "type": "Public", "subnets": [{"subnetId": "subnet-01b65899962035ac5", "cidr": "10.7.16.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-07922685bfa69eff6"}, {"subnetId": "subnet-0188d03dddbc111b5", "cidr": "10.7.0.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0130414655c80212a"}, {"subnetId": "subnet-03fcf4bf0a127fb74", "cidr": "10.7.32.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0e11917fb76309e95"}]}, {"name": "Isolated", "type": "Isolated", "subnets": [{"subnetId": "subnet-02f5a9da15203742e", "cidr": "10.7.112.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0a5b3e1db85070c47"}, {"subnetId": "subnet-0bbe2fee87dec5b91", "cidr": "10.7.96.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0bd59af08875042bf"}, {"subnetId": "subnet-0cf8126dc47b73ebd", "cidr": "10.7.128.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-059093d646f6e08bf"}]}, {"name": "Private", "type": "Private", "subnets": [{"subnetId": "subnet-02a31945b6f865282", "cidr": "10.7.64.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-09cb0ec2ba126cdcb"}, {"subnetId": "subnet-0b5c45a4267e14ab3", "cidr": "10.7.48.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-06ca3504a4a0ee75a"}, {"subnetId": "subnet-0cbb7eb62131d973b", "cidr": "10.7.80.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-060fefb8c881ef4a7"}]}]}}