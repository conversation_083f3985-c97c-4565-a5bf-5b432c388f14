import os

import aws_cdk as cdk
from aws_cdk import (
    aws_ec2 as ec2,
    aws_ecr_assets as ecr_assets,
    aws_ecs as ecs,
    aws_events as events,
    aws_events_targets as targets,
    aws_iam as iam,
    aws_logs as logs,
)
from constants import (
    AI_GATEWAY_SUB_DOMAIN,
    DAYTONA_SERVER_URL,
    SUPABASE_URL_MAP,
    SUPER_AGENT_API_SUB_DOMAIN,
)
from constructs import Construct
from ninja_common.stages.config import StageConfig
from ninja_common.stages.exports import StageExports, StageSecrets
from ninja_common_infra.constructs import NinjaFargateAlbApi, NinjaStack
from services.config import FEEDBACK_SERVICE_BASE_URL_ENV_VAR
from utils.secret_manager_utils import env_secret_from_secrets_manager
from utils.utils import get_service_base_url

from infra.core import CoreStack
from infra.monitoring import create_dashboard, create_monitoring_resources
from infra.utils.utils import get_super_agent_api_url, get_super_agent_ui_url


class SuperAgentApiStack(NinjaStack):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        stage_config: StageConfig,
        core_stack: CoreStack,
        **kwargs,
    ) -> None:
        super().__init__(scope, construct_id, stage_config=stage_config, **kwargs)
        self.construct_id = "NinjaSuperAgentApi"
        self.cognito_user_pool_id = cdk.Fn.import_value(StageExports.COGNITO_USER_POOL_ID.export)
        self.cognito_pool_client_id = cdk.Fn.import_value(
            StageExports.COGNITO_USER_POOL_CLIENT_ID.export
        )
        self.cluster = ecs.Cluster(
            self,
            "EcsCluster",
            vpc=self.vpc,
            cluster_name="ninja-super-agent-api-ecs-cluster",
        )

        self.container_image = ecs.ContainerImage.from_asset(
            directory=self.service_root_path,
            ignore_mode=cdk.IgnoreMode.DOCKER,
            file=str(os.path.join("docker", "backend.dockerfile")),
            build_args={"NINJA_CODE_ARTIFACT_TOKEN": os.environ["NINJA_CODE_ARTIFACT_TOKEN"]},
            platform=ecr_assets.Platform.LINUX_AMD64,
            invalidation=ecr_assets.DockerImageAssetInvalidationOptions(build_args=False),
        )

        self.secrets = {
            StageSecrets.SUPER_AGENT_API_KEY.name: env_secret_from_secrets_manager(
                self, StageSecrets.SUPER_AGENT_API_KEY
            ),
        }

        self.base_environment = {
            "REDIS_HOST": core_stack.redis_address,
            "REDIS_PORT": core_stack.redis_port,
            "SUPER_AGENT_FRONTEND_URL": get_super_agent_ui_url(stage_config),
            "SUPABASE_URL": SUPABASE_URL_MAP[stage_config.stage_name],
            "DAYTONA_SERVER_URL": DAYTONA_SERVER_URL,
            "DAYTONA_TARGET": "us",
            "OPENAI_API_BASE": f"{get_service_base_url(AI_GATEWAY_SUB_DOMAIN, stage_config)}/v2",
            "MODEL_TO_USE": "alias/openai/gpt-4.1",
            FEEDBACK_SERVICE_BASE_URL_ENV_VAR: get_service_base_url("feedback", stage_config),
            "WEBSITE_DEPLOYMENT_BUCKET": core_stack.website_deployment_bucket.bucket_name,
            "WEBSITE_DEPLOYMENT_CLOUDFRONT_ID": core_stack.website_deployment_distribution.distribution_id,
            "WEBSITE_DEPLOYMENT_URL": core_stack.website_deployment_url,
        }

        api = NinjaFargateAlbApi(
            self,
            self.construct_id,
            cluster=self.cluster,
            public_api=True,
            api_sub_domain=SUPER_AGENT_API_SUB_DOMAIN,
            container_image=self.container_image,
            memory_limit_mib=2 * 1024,
            cpu=1024,
            desired_count=1 if stage_config.stage_name == "dev" else None,
            secrets=self.secrets,
            environment=self.base_environment,
        )
        # Give permissions to upload to website bucket
        core_stack.website_deployment_bucket.grant_put(api.task_role)
        core_stack.website_deployment_distribution.grant_create_invalidation(api.task_role)
        core_stack.website_deployment_distribution.grant(
            api.task_role, "cloudfront:GetInvalidation"
        )

        # Set tmp volume in ECS task container to support file upload that larger than 1MB
        # Reference: https://repost.aws/questions/QUnPt8lYtKThud-OUp9PTMqQ/ecs-with-read-only-root-file-system-how-to-define-volumes-to-use-ephemeral-storage-and-address-them-within-docker-container
        api.service.task_definition.add_volume(name="tmp")
        api.service.task_definition.default_container.add_mount_points(
            ecs.MountPoint(source_volume="tmp", container_path="/tmp", read_only=False)
        )
        self.create_archive_old_sandboxes_task()
        self.create_canary_task()

        create_monitoring_resources(self)
        create_dashboard(self)

    def create_archive_old_sandboxes_task(self):
        # Create task definition for scheduled scripts
        script_task_definition = ecs.FargateTaskDefinition(
            self,
            f"{self.construct_id}-ArchiveOldSandboxesScriptTaskDefinition",
            memory_limit_mib=1024,  # Adjust based on script requirements
            cpu=512,
        )

        # Create log group for scheduled tasks
        script_log_group = logs.LogGroup(
            self,
            f"{self.construct_id}-ArchiveOldSandboxesScriptLogGroup",
            retention=logs.RetentionDays.ONE_MONTH,
        )

        # Add container to task definition
        script_container_name = f"{self.construct_id}-ArchiveOldSandboxesScriptContainer"
        script_container = script_task_definition.add_container(
            script_container_name,
            image=self.container_image,
            memory_limit_mib=1024,
            cpu=512,
            environment=self.base_environment,
            secrets=self.secrets,
            logging=ecs.LogDrivers.aws_logs(
                stream_prefix="scheduled-scripts",
                log_group=script_log_group,
            ),
        )

        # Create scheduled task for archive_old_sandboxes
        archive_schedule = events.Rule(
            self,
            f"{self.construct_id}-ArchiveOldSandboxesSchedule",
            description="Daily archive of old sandboxes",
            schedule=events.Schedule.rate(
                cdk.Duration.hours(12)
                if self.stage_config.stage_name == "prod"
                else cdk.Duration.hours(1)
            ),
        )

        # Add ECS task target
        archive_schedule.add_target(
            targets.EcsTask(
                cluster=self.cluster,
                task_definition=script_task_definition,
                launch_type=ecs.LaunchType.FARGATE,
                platform_version=ecs.FargatePlatformVersion.LATEST,
                subnet_selection=ec2.SubnetSelection(
                    subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS
                ),
                container_overrides=[
                    targets.ContainerOverride(
                        container_name=script_container_name,
                        command=[
                            "/bin/bash",
                            "-c",
                            "echo 'TRUE' | python /code/src/utils/scripts/archive_old_sandboxes.py --days 1",
                        ],
                    )
                ],
            )
        )

    def create_canary_task(self):
        canary_env_vars = {
            "SUPERNINJA_API_URL": get_super_agent_api_url(self.stage_config),
            "SUPERNINJA_CANARY_USERNAME": "Plain_SuperNinjaCanaryUser",
            "NINJA_USER_POOL_ID": self.cognito_user_pool_id,
            "NINJA_USER_POOL_CLIENT_ID": self.cognito_pool_client_id,
        }

        # Create task definition for scheduled scripts
        script_task_definition = ecs.FargateTaskDefinition(
            self,
            f"{self.construct_id}-CanaryTaskDefinition",
            memory_limit_mib=1024,  # Adjust based on script requirements
            cpu=512,
        )

        script_task_definition.add_to_task_role_policy(
            iam.PolicyStatement(
                actions=[
                    "cognito-idp:InitiateAuth",  # For user authentication
                    "cognito-idp:RespondToAuthChallenge",  # For completing auth challenges
                ],
                resources=[f"arn:aws:cognito-idp:*:*:userpool/{self.cognito_user_pool_id}"],
            )
        )

        # Create log group for scheduled tasks
        script_log_group = logs.LogGroup(
            self,
            f"{self.construct_id}-CanaryLogGroup",
            retention=logs.RetentionDays.ONE_MONTH,
        )

        # Add container to task definition
        script_container_name = f"{self.construct_id}-CanaryContainer"
        script_container = script_task_definition.add_container(
            script_container_name,
            image=self.container_image,
            memory_limit_mib=1024,
            cpu=512,
            environment=dict(self.base_environment, **canary_env_vars),
            secrets=self.secrets,
            logging=ecs.LogDrivers.aws_logs(
                stream_prefix="scheduled-scripts",
                log_group=script_log_group,
            ),
        )

        # Create scheduled task for archive_old_sandboxes
        canary_schedule = events.Rule(
            self,
            f"{self.construct_id}-CanarySchedule",
            description="SuperNinja Canary",
            schedule=events.Schedule.rate(cdk.Duration.hours(1)),
        )

        # Add ECS task target
        canary_schedule.add_target(
            targets.EcsTask(
                cluster=self.cluster,
                task_definition=script_task_definition,
                launch_type=ecs.LaunchType.FARGATE,
                platform_version=ecs.FargatePlatformVersion.LATEST,
                subnet_selection=ec2.SubnetSelection(
                    subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS
                ),
                container_overrides=[
                    targets.ContainerOverride(
                        container_name=script_container_name,
                        command=[
                            "/bin/bash",
                            "-c",
                            "python /code/src/utils/scripts/backend_canary.py",
                        ],
                    )
                ],
            )
        )
