import os

import aws_cdk as cdk
from aws_cdk import aws_ec2 as ec2, aws_ecr_assets as ecr_assets, aws_ecs as ecs
from aws_cdk.aws_elasticloadbalancingv2 import (
    ApplicationListenerRule,
    ApplicationProtocol,
    ApplicationTargetGroup,
    ListenerAction,
    ListenerCondition,
)
from constants import SUPABASE_ANON_KEY_MAP, SUPABASE_URL_MAP, SUPER_AGENT_UI_SUB_DOMAIN
from constructs import Construct
from ninja_common.stages.config import StageConfig
from ninja_common.stages.exports import StageExports
from ninja_common_infra.constructs import NinjaFargateAlbApi, NinjaStack
from utils.utils import get_super_agent_api_url, get_super_agent_ui_domain, get_super_agent_ui_url

from infra.core import CoreStack
from infra.utils.utils import get_root_ui_domain


class SuperAgentUIStack(NinjaStack):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        stage_config: StageConfig,
        core_stack: CoreStack,
        **kwargs,
    ) -> None:
        super().__init__(scope, construct_id, stage_config=stage_config, **kwargs)

        cluster = ecs.Cluster(
            self,
            "EcsCluster",
            vpc=self.vpc,
            cluster_name="ninja-super-agent-ui-ecs-cluster",
        )

        container_image = ecs.ContainerImage.from_asset(
            directory=self.service_root_path,
            ignore_mode=cdk.IgnoreMode.DOCKER,
            file=str(os.path.join("docker", "frontend.dockerfile")),
            build_args={
                "NINJA_CODE_ARTIFACT_TOKEN": os.environ["NINJA_CODE_ARTIFACT_TOKEN"],
                "NINJA_STAGE_NAME": stage_config.stage_name,
            },
            platform=ecr_assets.Platform.LINUX_AMD64,
            invalidation=ecr_assets.DockerImageAssetInvalidationOptions(build_args=False),
        )

        self.api = NinjaFargateAlbApi(
            self,
            "NinjaSuperAgentUIApi",
            cluster=cluster,
            api_sub_domain=SUPER_AGENT_UI_SUB_DOMAIN,
            container_image=container_image,
            memory_limit_mib=2 * 1024,
            cpu=1024,
            desired_count=1 if stage_config.stage_name == "dev" else None,
            service_port=80,
            environment={
                "NEXT_PUBLIC_ENV_MODE": stage_config.stage_name,
                "NEXT_PUBLIC_SUPABASE_URL": SUPABASE_URL_MAP[stage_config.stage_name],
                "NEXT_PUBLIC_SUPABASE_ANON_KEY": SUPABASE_ANON_KEY_MAP[stage_config.stage_name],
                "NEXT_PUBLIC_BACKEND_URL": get_super_agent_api_url(stage_config),
                "NEXT_PUBLIC_URL": get_super_agent_ui_url(stage_config),
                "NEXT_PUBLIC_GOOGLE_CLIENT_ID": "",
                "NEXT_PUBLIC_COGNITO_USER_POOL_ID": cdk.Fn.import_value(
                    StageExports.COGNITO_USER_POOL_ID.export
                ),
                "NEXT_PUBLIC_COGNITO_USER_POOL_CLIENT_ID": cdk.Fn.import_value(
                    StageExports.COGNITO_USER_POOL_CLIENT_ID.export
                ),
                "NEXT_PUBLIC_COGNITO_OAUTH_DOMAIN": f"auth.atlas.{stage_config.dns_zone_name}",
                "NEXT_PUBLIC_COGNITO_COOKIES_DOMAIN": get_root_ui_domain(stage_config),
                "NEXT_PUBLIC_MY_NINJA_URL": f"https://{stage_config.ui_zone_name}",
            },
        )

        self._attach_to_fe_alb(core_stack=core_stack)

    def _attach_to_fe_alb(self, core_stack: CoreStack):
        # allow public LB to call fargate service
        self.api.service.connections.allow_from(core_stack.private_alb, ec2.Port.tcp(80))

        fe_target_group = ApplicationTargetGroup(
            self,
            "FETargetGroup",
            port=80,
            vpc=self.vpc,
            protocol=ApplicationProtocol.HTTP,
            targets=[
                self.api.service.load_balancer_target(container_name="service", container_port=80)
            ],
        )
        # Configure health check
        fe_target_group.configure_health_check(
            path="/health",
            healthy_threshold_count=2,
            unhealthy_threshold_count=2,
            interval=cdk.Duration.seconds(30),
            timeout=cdk.Duration.seconds(15),
            healthy_http_codes="200",
        )

        ApplicationListenerRule(
            self,
            "PublicFETargetRule",
            listener=core_stack.private_alb_listener,
            action=ListenerAction.forward([fe_target_group]),
            conditions=[
                ListenerCondition.host_headers(
                    values=[get_super_agent_ui_domain(self.stage_config)]
                ),
            ],
            priority=1002,  # let any other rules chance to apply before the domain match
        )
