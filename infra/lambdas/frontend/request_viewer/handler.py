import base64
import logging

from shared.utils.constants import (
    API_AUTH_URI_PATH,
    REDIRECT_HOST_MAP,
    UI_AUTH_EDGE_LAMBDA_AUTH_COOKIE,
    UI_AUTH_EDGE_LAMBDA_AUTH_HEADER,
    UI_AUTH_EDGE_LAMBDA_KMS_ID,
    UI_AUTH_EDGE_LAMBDA_PASSWORD_SECRET_NAME,
    VIEWER_ENV,
)
from shared.utils.cookie_utils import from_cfn_cookies_to_dict, generate_auth_cookie
from shared.utils.html_utils import generate_static_auth_response
from shared.utils.http_utils import (
    access_denied_response,
    redirect_to_main_page_with_cookie_response,
)
from shared.utils.kms_utils import get_kms_client, verify_password
from shared.utils.sm_utils import get_password_from_sm

logging.getLogger().setLevel(logging.INFO)


def handler(event, context):
    request = event["Records"][0]["cf"]["request"]
    headers = request["headers"]
    uri = request["uri"]

    hashed_password_base64_str = get_password_from_sm(UI_AUTH_EDGE_LAMBDA_PASSWORD_SECRET_NAME)
    if uri.startswith(API_AUTH_URI_PATH):
        if UI_AUTH_EDGE_LAMBDA_AUTH_HEADER not in headers:
            logging.info("Auth endpoint but no auth header is provided")
            return access_denied_response()

        plain_password = headers[UI_AUTH_EDGE_LAMBDA_AUTH_HEADER][0]["value"]
        hashed_password = base64.b64decode(hashed_password_base64_str)

        if verify_password(
            plain_password=plain_password,
            hashed_password=hashed_password,
            kms_key_id=UI_AUTH_EDGE_LAMBDA_KMS_ID,
        ):
            logging.info("Passwords matched, generating access cookie")
            auth_cookie = generate_auth_cookie(
                encrypted_password=hashed_password_base64_str,
                domain=REDIRECT_HOST_MAP[VIEWER_ENV]
                if VIEWER_ENV in REDIRECT_HOST_MAP
                else ".myninja.ai",
            )
            return redirect_to_main_page_with_cookie_response(
                location=uri.replace(API_AUTH_URI_PATH, "/"), cookie=auth_cookie
            )
        else:
            logging.info("Passwords didn't match")
            return access_denied_response()

    cookies = from_cfn_cookies_to_dict(headers["cookie"]) if "cookie" in headers else {}
    if UI_AUTH_EDGE_LAMBDA_AUTH_COOKIE in cookies:
        logging.info("Verifying auth cookie")
        password_cookie = cookies[UI_AUTH_EDGE_LAMBDA_AUTH_COOKIE]

        if password_cookie != hashed_password_base64_str:
            logging.info("Wrong cookie value, redirecting to the auth form")
            return generate_static_auth_response()
    else:
        logging.info("No auth cookie, redirecting to the auth form")
        return generate_static_auth_response()

    return request


# preload resources on lambda's cold-start
get_password_from_sm(UI_AUTH_EDGE_LAMBDA_PASSWORD_SECRET_NAME)
get_kms_client()
generate_static_auth_response()
