import logging
from functools import cache

import boto3
import botocore.exceptions


@cache
def get_kms_client():
    return boto3.client("kms", region_name="us-west-2")


def verify_password(plain_password: str, hashed_password: bytes, kms_key_id) -> bool:
    try:
        return get_kms_client().verify(
            KeyId=kms_key_id,
            Message=plain_password.encode(),
            Signature=hashed_password,
            SigningAlgorithm="RSASSA_PKCS1_V1_5_SHA_256",
        )["SignatureValid"]
    except botocore.exceptions.ClientError as error:
        error_code = error.response["Error"]["Code"]
        if error_code == "KMSInvalidSignatureException":
            return False
        else:
            logging.info(f"Unexpected boto3 error code: {error_code}")
            return False
