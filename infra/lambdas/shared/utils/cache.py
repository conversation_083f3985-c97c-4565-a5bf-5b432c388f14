import time
from functools import wraps


class TTLCache:
    def __init__(self, ttl=60):  # 1 minute default TTL
        self.cache = {}
        self.ttl = ttl

    def get(self, key):
        if key in self.cache:
            value, expiry = self.cache[key]
            if time.time() < expiry:
                return value
            else:
                del self.cache[key]
        return None

    def set(self, key, value):
        expiry = time.time() + self.ttl
        self.cache[key] = (value, expiry)

    def delete(self, key):
        if key in self.cache:
            del self.cache[key]


def ttl_cache(ttl=60):
    cache = TTLCache(ttl)

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            key = str(args) + str(kwargs)
            result = cache.get(key)
            if result is None:
                result = func(*args, **kwargs)
                cache.set(key, result)
            return result

        return wrapper

    return decorator
