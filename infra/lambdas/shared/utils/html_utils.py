from functools import cache


@cache
def generate_static_auth_response():
    html = """
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Auth page</title>
    <script type="module">
        document.getElementById('ninjaForm').addEventListener('submit', function(event) {
         event.preventDefault();
         document.getElementById('submitButton').disabled=true;
         var password = document.getElementById('ninjaPass').value;
         var xhr = new XMLHttpRequest();
         xhr.open('GET', '/api/auth', true);
         xhr.setRequestHeader('X-Ninja-Auth', password);
         xhr.send();
         xhr.onload = () => {
            window.location.reload();
         };
         xhr.onerror = () => {
            document.getElementById('submitButton').disabled=false;
         };
        });
    </script>
</head>
<body>
<div style="display: flex; flex-direction: column; align-items: center; width: 100%; height: 100%; background-color: #f0f0f0; padding: 20px; padding-top: 200px;">
    <div style="margin-bottom: 20px;">
        <h2 style="font-size: 24px; font-weight: bold; color: #333; margin: 0;">Enter a password to proceed</h2>
    </div>
    <div style="width: 300px; padding: 20px; border: 1px solid #ddd; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);">
        <form action="/api/auth" method="post" id="ninjaForm">
             <input type="password" name="password" placeholder="Enter your password" id="ninjaPass"
                    style="width: 100%; padding: 10px; margin-bottom: 10px; border: 1px solid #ccc; border-radius: 5px;">
             <input type="submit" value="Submit" id="submitButton"
                    style="width: 100%; padding: 10px; background-color: #4CAF50; color: #fff; border: none; border-radius: 5px; cursor: pointer;">
        </form>
    </div>
</div>

</body>
</html>
       """

    headers = {
        "cache-control": [{"key": "Cache-Control", "value": "no-cache"}],
        "content-type": [{"key": "Content-Type", "value": "text/html"}],
    }

    # Return the response
    return {"status": 200, "headers": headers, "body": html}
