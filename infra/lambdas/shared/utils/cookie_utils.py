import datetime
from http.cookies import SimpleCookie

from shared.utils.constants import UI_AUTH_EDGE_LAMBDA_AUTH_COOKIE


def generate_auth_cookie(encrypted_password, domain: str | None = None) -> SimpleCookie:
    expires = datetime.datetime.now(datetime.UTC) + datetime.timedelta(days=365)

    cookie = SimpleCookie()
    cookie[UI_AUTH_EDGE_LAMBDA_AUTH_COOKIE] = encrypted_password
    cookie[UI_AUTH_EDGE_LAMBDA_AUTH_COOKIE]["secure"] = True
    cookie[UI_AUTH_EDGE_LAMBDA_AUTH_COOKIE]["httponly"] = True
    cookie[UI_AUTH_EDGE_LAMBDA_AUTH_COOKIE]["path"] = "/"
    # with OAuth our webapp is opened from Cognito/Google website and with Strict the cookies gets blocked
    cookie[UI_AUTH_EDGE_LAMBDA_AUTH_COOKIE]["samesite"] = "Lax"
    cookie[UI_AUTH_EDGE_LAMBDA_AUTH_COOKIE]["expires"] = expires.strftime(
        "%a, %d %b %Y %H:%M:%S GMT"
    )

    if domain:
        # Set the cookie's domain (optional)
        cookie[UI_AUTH_EDGE_LAMBDA_AUTH_COOKIE]["domain"] = domain

    return cookie


def from_cfn_cookies_to_dict(req_cookies):
    cookie_loader = SimpleCookie()
    cookie_loader.load(req_cookies[0]["value"])
    return {k: v.value for k, v in cookie_loader.items()}
