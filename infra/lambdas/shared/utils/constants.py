import logging
import os

API_AUTH_URI_PATH = "/api/auth"

UI_AUTH_EDGE_LAMBDA_AUTH_HEADER = "x-ninja-auth"
UI_AUTH_EDGE_LAMBDA_AUTH_COOKIE = "__Secure-x_ninja_auth"

UI_AUTH_EDGE_LAMBDA_PASSWORD_SECRET_NAME = "UIAuthPassword"
UI_AUTH_EDGE_LAMBDA_KMS_ID = "alias/ui-auth-access"

logging.getLogger().setLevel(logging.INFO)

REDIRECT_HOST_MAP = {"beta": "betamyninja.ai", "gamma": "gammamyninja.ai", "prod": "myninja.ai"}

# because edge lambda does not support ENVS we have to use this workaround
VIEWER_ENV = (
    os.environ["AWS_LAMBDA_FUNCTION_NAME"].rsplit("-", 1)[1]
    if "AWS_LAMBDA_FUNCTION_NAME" in os.environ
    else "dev"
)
