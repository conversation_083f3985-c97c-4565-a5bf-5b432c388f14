from functools import cache
from http.cookies import Simple<PERSON>ookie


@cache
def access_denied_response():
    return {
        "status": "403",
    }


def redirect_to_main_page_with_cookie_response(location: str, cookie: SimpleCookie):
    return {
        "status": "302",
        "statusDescription": "Found",
        "headers": {
            "location": [{"key": "Location", "value": location}],
            "set-cookie": [{"key": "set-cookie", "value": cookie.output(header="")}],
        },
    }
