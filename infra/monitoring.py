from aws_cdk import Duration, aws_cloudwatch as cw
from ninja_common_infra.constructs import NinjaStack
from ninja_common_infra.constructs.alarms import NinjaAlarm
from ninja_common_infra.helpers.alarms import Impact

from backend.src.utils.constants import SUPER_AGENT_SERVICE_NAME


def get_backend_canary_monitoring_metric() -> cw.Metric:
    return cw.Metric(
        namespace=SUPER_AGENT_SERVICE_NAME,
        metric_name="CanarySuccess",
        dimensions_map={"CanaryType": "BackendCanary"},
        period=Duration.hours(1),
        statistic="max",
    )


def create_dashboard(stack: NinjaStack):
    dashboard = cw.Dashboard(
        stack, "SuperNinjaBackendDashboard", dashboard_name="SuperNinja-Backend-Dashboard"
    )
    graph = cw.GraphWidget(
        title="Canary Status",
        left=[
            get_backend_canary_monitoring_metric(),
        ],
    )
    # Add the graph to the dashboard
    dashboard.add_widgets(graph)


def create_monitoring_resources(stack: NinjaStack):
    _create_canary_monitoring_resources(stack)
    _create_sandbox_archival_monitoring_resources(stack)


def _create_sandbox_archival_monitoring_resources(stack: NinjaStack):
    sandbox_archival_monitoring_metric = cw.Metric(
        namespace=SUPER_AGENT_SERVICE_NAME,
        metric_name="SuccessfulArchivedSandboxes",
        statistic="max",
        period=Duration.hours(12),
    )
    stack.sandbox_archival_monitoring_alarm = NinjaAlarm(
        stack,
        "SandboxArchivalMonitoringAlarm",
        component_names=[],
        short_description="SuperNinja_Sandbox_Archival_Failed",
        impact=Impact.HIGH,
        alarm_description=f"""
The purpose of this alarm is to catch when sandbox archival script is failing
""",
        metric=sandbox_archival_monitoring_metric,
        evaluation_periods=2,
        comparison_operator=cw.ComparisonOperator.LESS_THAN_OR_EQUAL_TO_THRESHOLD,
        threshold=0,
        treat_missing_data=cw.TreatMissingData.BREACHING,
    )


def _create_canary_monitoring_resources(stack: NinjaStack):
    stack.backend_canary_monitoring_alarm = NinjaAlarm(
        stack,
        "BackendCanaryMonitoringAlarm",
        component_names=[],
        short_description="SuperNinja_Backend_Canary_Failed",
        impact=Impact.CRITICAL,
        alarm_description=f"""
The purpose of this alarm is to catch when some component involved in the SuperNinja workflow
is failing.
""",
        metric=get_backend_canary_monitoring_metric(),
        evaluation_periods=2,
        comparison_operator=cw.ComparisonOperator.LESS_THAN_THRESHOLD,
        threshold=1,
        treat_missing_data=cw.TreatMissingData.BREACHING,
    )
