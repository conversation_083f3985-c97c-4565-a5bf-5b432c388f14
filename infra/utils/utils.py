from ninja_common.stages.config import StageConfig

from infra.constants import (
    SUPER_AGENT_API_SUB_DOMAIN,
    SUPER_AGENT_DEPLOYED_WEBSITE_DOMAIN,
    SUPER_AGENT_UI_SUB_DOMAIN,
)


def get_service_base_url(service_sub_domain: str, stage_config: StageConfig) -> str:
    # Dev point to beta
    if stage_config.is_dev():
        return f"https://{service_sub_domain}.beta.myninja.ai"

    return f"https://{service_sub_domain}.{stage_config.dns_zone_name}"


def get_root_ui_domain(stage_config: StageConfig):
    return stage_config.ui_zone_name


def get_super_agent_api_domain(stage_config: StageConfig):
    return f"{SUPER_AGENT_API_SUB_DOMAIN}.public.{stage_config.dns_zone_name}"


def get_super_agent_api_url(stage_config: StageConfig):
    return f"https://{SUPER_AGENT_API_SUB_DOMAIN}.public.{stage_config.dns_zone_name}/api"


def get_super_agent_ui_domain(stage_config: StageConfig):
    return f"{SUPER_AGENT_UI_SUB_DOMAIN}.{get_root_ui_domain(stage_config)}"


def get_super_agent_ui_url(stage_config: StageConfig):
    return f"https://{get_super_agent_ui_domain(stage_config)}"


def get_super_agent_website_domain(stage_config: StageConfig):
    return f"{SUPER_AGENT_DEPLOYED_WEBSITE_DOMAIN}.{get_super_agent_ui_domain(stage_config)}"
