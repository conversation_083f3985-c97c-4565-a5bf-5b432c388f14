import aws_cdk as cdk
from aws_cdk import aws_ecs as ecs, aws_secretsmanager as secrets_manager
from aws_cdk.aws_secretsmanager import ISecret
from constructs import Construct
from ninja_common.stages.exports import StageSecrets


def get_secret_from_secrets_manager(scope: Construct, stage_secret: StageSecrets) -> ISecret:
    secret = secrets_manager.Secret.from_secret_name_v2(
        scope,
        stage_secret.value,
        secret_name=cdk.Fn.import_value(stage_secret.export),
    )
    return secret


def env_secret_from_secrets_manager(scope: Construct, stage_secret: StageSecrets) -> ecs.Secret:
    secret = get_secret_from_secrets_manager(scope, stage_secret)
    return ecs.Secret.from_secrets_manager(secret)
