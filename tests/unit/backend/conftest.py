from uuid import UUID

import jwt
import pytest
from fastapi.testclient import Test<PERSON>lient
from src.api import app


@pytest.fixture
def sample_user_id():
    return UUID("e1014ae2-66cc-4691-befa-2585c2bf8460")


@pytest.fixture
def sample_http_headers(sample_user_id):
    payload = {"sub": str(sample_user_id), "custom:ninja_user_id": str(sample_user_id)}
    token = jwt.encode(payload, "test-secret", algorithm="HS256")
    return {
        "Authorization": f"Bearer {token}",
    }


@pytest.fixture()
def test_client(sample_http_headers):
    return TestClient(app, headers=sample_http_headers)
