from unittest.mock import AsyncMock, MagicMock
from uuid import <PERSON><PERSON><PERSON>

import pytest
from agent.models import Agent<PERSON><PERSON><PERSON><PERSON>back, AgentRunFeedbackUpsertRequest
from fastapi import status
from ninja_feedback_client.client import Feedback<PERSON>piAsyncClient
from ninja_feedback_client.models import (
    ApiFeedbackV2,
    FeedbackUpsertRequest,
    PublicCreateFeedbackV2,
    RatingFeedback,
    SuperNinjaAgentRunFeedback,
)


@pytest.fixture
def mock__update_feedback_on_agent_run_row(mocker):
    mock = AsyncMock()
    mocker.patch("services.feedback._update_feedback_on_agent_run_row", mock)
    return mock


@pytest.fixture
def mock__get_feedaback_on_agent_run_row(mocker):
    mock = AsyncMock()
    mocker.patch("services.feedback._get_feedaback_on_agent_run_row", mock)
    return mock


@pytest.fixture
def mock__generate_new_feedback_id(mocker):
    mock = MagicMock()
    mocker.patch("services.feedback._generate_new_feedback_id", mock)
    return mock


@pytest.fixture
def mock_feedback_service_client(mocker):
    mock_client = MagicMock(spec=FeedbackApiAsyncClient)
    mock_client.submit_feedback = AsyncMock()

    mocker.patch(
        "services.feedback.get_feedback_service_client",
        return_value=mock_client,
    )

    return mock_client


@pytest.fixture
def mock_db_client(mocker):
    mock_client = MagicMock()

    mocker.patch("agent.api.get_db_client", return_value=mock_client)

    return mock_client


def test_upsert_agent_run_feedback__existing_feedback(
    test_client,
    sample_user_id,
    mock__update_feedback_on_agent_run_row,
    mock__get_feedaback_on_agent_run_row,
    mock_feedback_service_client,
    mock_db_client,
    mocker,
):
    # Arrange
    feedback_id = UUID("83915201-3a4e-4809-94c9-29bab1080981")
    agent_run_id = UUID("aeaff856-b4f0-403f-8f0b-285eca4df98e")

    data_before = SuperNinjaAgentRunFeedback(
        feedback_type="super_ninja_agent_run",
        rating=RatingFeedback(
            rating=5,
            reason="Excellent performance",
        ),
        agent_run_id=agent_run_id,
    )

    new_data = SuperNinjaAgentRunFeedback(
        feedback_type="super_ninja_agent_run",
        rating=RatingFeedback(
            rating=4,
            reason="Good performance, but could improve",
        ),
        agent_run_id=agent_run_id,
    )

    existing_feedback = AgentRunFeedback(feedback_id=feedback_id, data=data_before)

    new_feedback = AgentRunFeedback(feedback_id=feedback_id, data=new_data)

    mock__get_feedaback_on_agent_run_row.return_value = existing_feedback

    mock_feedback_service_client.upsert_feedback.return_value = ApiFeedbackV2(
        user_id=sample_user_id, feedback_id=feedback_id, data=new_data
    )

    request = AgentRunFeedbackUpsertRequest(data=new_data)

    # Act
    response = test_client.put(
        f"/api/agent-run/{agent_run_id}/feedback",
        json=request.model_dump(mode="json"),
    )

    # Assert API response
    assert response.status_code == status.HTTP_202_ACCEPTED
    parsed_response = AgentRunFeedback.model_validate(response.json())
    assert parsed_response.model_dump() == new_feedback.model_dump()

    # Assert correct calls made in service
    mock_feedback_service_client.upsert_feedback.assert_called_once()
    upsert_call_args = mock_feedback_service_client.upsert_feedback.call_args[0]
    assert upsert_call_args[0] == str(sample_user_id)
    assert upsert_call_args[1] == feedback_id
    assert upsert_call_args[2].data.model_dump() == new_data.model_dump()

    mock__update_feedback_on_agent_run_row.assert_called_once()
    update_call_args = mock__update_feedback_on_agent_run_row.call_args[0]
    assert update_call_args[1] == agent_run_id
    assert (
        update_call_args[2].model_dump()
        == AgentRunFeedback(data=new_data, feedback_id=feedback_id).model_dump()
    )


def test_upsert_agent_run_feedback__nonexistent_feedback(
    test_client,
    sample_user_id,
    mock__update_feedback_on_agent_run_row,
    mock__get_feedaback_on_agent_run_row,
    mock__generate_new_feedback_id,
    mock_feedback_service_client,
    mock_db_client,
    mocker,
):
    # Arrange
    feedback_id = UUID("83915201-3a4e-4809-94c9-29bab1080981")
    agent_run_id = UUID("aeaff856-b4f0-403f-8f0b-285eca4df98e")

    new_data = SuperNinjaAgentRunFeedback(
        feedback_type="super_ninja_agent_run",
        rating=RatingFeedback(
            rating=4,
            reason="Good performance, but could improve",
        ),
        agent_run_id=agent_run_id,
    )

    new_feedback = AgentRunFeedback(feedback_id=feedback_id, data=new_data)

    mock__get_feedaback_on_agent_run_row.return_value = None

    mock__generate_new_feedback_id.return_value = feedback_id

    mock_feedback_service_client.upsert_feedback.return_value = ApiFeedbackV2(
        user_id=sample_user_id, feedback_id=feedback_id, data=new_data
    )

    request = AgentRunFeedbackUpsertRequest(data=new_data)

    # Act
    response = test_client.put(
        f"/api/agent-run/{agent_run_id}/feedback",
        json=request.model_dump(mode="json"),
    )

    # Assert API response
    assert response.status_code == status.HTTP_202_ACCEPTED
    parsed_response = AgentRunFeedback.model_validate(response.json())
    assert parsed_response.model_dump() == new_feedback.model_dump()

    # Assert correct calls made in service
    mock_feedback_service_client.upsert_feedback.assert_called_once()
    upsert_call_args = mock_feedback_service_client.upsert_feedback.call_args[0]
    assert upsert_call_args[0] == str(sample_user_id)
    assert upsert_call_args[1] == feedback_id
    assert upsert_call_args[2].data.model_dump() == new_data.model_dump()

    mock__update_feedback_on_agent_run_row.assert_called_once()
    update_call_args = mock__update_feedback_on_agent_run_row.call_args[0]
    assert update_call_args[1] == agent_run_id
    assert (
        update_call_args[2].model_dump()
        == AgentRunFeedback(data=new_data, feedback_id=feedback_id).model_dump()
    )
