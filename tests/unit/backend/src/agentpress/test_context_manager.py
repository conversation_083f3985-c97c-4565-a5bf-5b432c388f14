from unittest.mock import AsyncMock
from uuid import uuid4

import pytest
from agentpress.context_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, LLMContextError

TEST_THREAD_ID: str = str(uuid4())
TEST_MODEL: str = "alias/test/test-model"
TEST_TOKEN_THRESHOLD: int = 100


@pytest.fixture
def context_manager():
    """Returns a context manager with low token threshold for testing"""
    return ContextManager(token_threshold=TEST_TOKEN_THRESHOLD)


@pytest.fixture
def messages():
    """Return an example chat history that would be valid for summarization"""
    return [
        {"role": "user", "content": "Hello"},
        {"role": "assistant", "content": "Hi, how can I help?"},
        {"role": "user", "content": "Summarize this thread."},
        {"role": "assistant", "content": "Sure, here is a summary."},
    ]


@pytest.fixture
def summary_response():
    """Return a summary response in the right structure"""

    class FakeResponse:
        class Choice:
            class Message:
                content = "This is a summary"

            message = Message()

        choices = [Choice()]

    return FakeResponse()


@pytest.fixture
def add_message_callback():
    return AsyncMock()


@pytest.fixture(autouse=True)
def mock_llm_call(mocker):
    mock = AsyncMock()
    mocker.patch("agentpress.context_manager.make_llm_api_call", mock)
    return mock


@pytest.fixture(autouse=True)
def mock_get_messages(mocker):
    mock = AsyncMock()
    mocker.patch.object(ContextManager, "get_messages_for_summarization", mock)
    return mock


@pytest.fixture(autouse=True)
def mock_token_count(mocker):
    mock = AsyncMock()
    mocker.patch.object(ContextManager, "get_thread_token_count", mock)
    return mock


async def test_create_summary_successful_summarization(
    context_manager,
    messages,
    summary_response,
    add_message_callback,
    mock_token_count,
    mock_get_messages,
    mock_llm_call,
):
    """Test a chat with enough tokens will be summarized successfully"""
    mock_token_count.return_value = 200  # Above threshold
    mock_get_messages.return_value = messages
    mock_llm_call.return_value = summary_response

    result = await context_manager.check_and_summarize_if_needed(
        thread_id=TEST_THREAD_ID, add_message_callback=add_message_callback, model=TEST_MODEL
    )

    assert result is True
    add_message_callback.assert_awaited_once()
    args = add_message_callback.call_args[1]
    assert args["type"] == "summary"
    assert "This is a summary" in args["content"]["content"]


async def test_create_summary_no_summarization_needed(
    context_manager, add_message_callback, mock_token_count
):
    """Test a chat with not enough tokens will not be summarized"""
    mock_token_count.return_value = 10  # Below threshold

    result = await context_manager.check_and_summarize_if_needed(
        thread_id=TEST_THREAD_ID, add_message_callback=add_message_callback, model=TEST_MODEL
    )

    assert result is False
    add_message_callback.assert_not_awaited()


async def test_create_summary_no_messages(
    context_manager, add_message_callback, mock_get_messages, mock_token_count
):
    """Test a chat with no messages will not be summarized"""
    mock_token_count.return_value = 200
    mock_get_messages.return_value = []

    result = await context_manager.check_and_summarize_if_needed(
        thread_id=TEST_THREAD_ID, add_message_callback=add_message_callback, model=TEST_MODEL
    )

    assert result is False
    add_message_callback.assert_not_awaited()


async def test_create_summary_too_few_messages(
    context_manager, add_message_callback, mock_get_messages, mock_token_count
):
    """Test a chat with too few messages will not be summarized"""
    mock_token_count.return_value = 200  # Above threshold
    mock_get_messages.return_value = [{"role": "user", "content": "Hi"}]  # Only 1 message

    result = await context_manager.check_and_summarize_if_needed(
        thread_id=TEST_THREAD_ID, add_message_callback=add_message_callback, model=TEST_MODEL
    )

    assert result is False
    add_message_callback.assert_not_awaited()


async def test_forced_summarization(
    context_manager,
    messages,
    summary_response,
    add_message_callback,
    mock_token_count,
    mock_get_messages,
    mock_llm_call,
):
    """Test a chat with not enough tokens will be summarized uf forced"""
    mock_token_count.return_value = 10  # Below threshold
    mock_get_messages.return_value = messages
    mock_llm_call.return_value = summary_response

    result = await context_manager.check_and_summarize_if_needed(
        thread_id=TEST_THREAD_ID,
        add_message_callback=add_message_callback,
        model=TEST_MODEL,
        force=True,
    )

    assert result is True
    add_message_callback.assert_awaited_once()


async def test_create_summary_error_llm_context_fallback_success(
    context_manager,
    messages,
    summary_response,
    add_message_callback,
    mock_token_count,
    mock_get_messages,
    mock_llm_call,
):
    """Test a chat with enough tokens will be successfully summarized by fallback if default summary out of context"""
    mock_token_count.return_value = 200
    mock_get_messages.return_value = messages

    # First call raises LLMContextError, second call returns summary
    mock_llm_call.side_effect = [LLMContextError("context error"), summary_response]

    result = await context_manager.check_and_summarize_if_needed(
        thread_id=TEST_THREAD_ID, add_message_callback=add_message_callback, model=TEST_MODEL
    )

    assert result is True
    assert mock_llm_call.call_count == 2
    add_message_callback.assert_awaited_once()


async def test_create_summary_error_llm_context_fallback_failure(
    context_manager,
    messages,
    add_message_callback,
    mock_token_count,
    mock_get_messages,
    mock_llm_call,
):
    """Test a chat with enough tokens will be raise exception if default and fallback summary out of context"""
    mock_token_count.return_value = 200
    mock_get_messages.return_value = messages

    # Both calls raise LLMContextError
    mock_llm_call.side_effect = [LLMContextError("context error"), LLMContextError("context error")]

    with pytest.raises(LLMContextError):
        await context_manager.check_and_summarize_if_needed(
            thread_id=TEST_THREAD_ID, add_message_callback=add_message_callback, model=TEST_MODEL
        )

    assert mock_llm_call.call_count == 2
    add_message_callback.assert_not_awaited()


async def test_create_summary_error_general_exception(
    context_manager,
    messages,
    add_message_callback,
    mock_token_count,
    mock_get_messages,
    mock_llm_call,
):
    """Test a chat with unexpected error thrown will not summarize"""
    mock_token_count.return_value = 200
    mock_get_messages.return_value = messages

    # LLM call raises unexpected exception
    mock_llm_call.side_effect = Exception("Unexpected error")

    result = await context_manager.check_and_summarize_if_needed(
        thread_id=TEST_THREAD_ID, add_message_callback=add_message_callback, model=TEST_MODEL
    )

    assert result is False
    add_message_callback.assert_not_awaited()
