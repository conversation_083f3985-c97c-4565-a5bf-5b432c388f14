# Quick Setup Guide for Memory Feature

## Overview

The memory feature has been successfully integrated into your agent using mem0ai. **No external services are required** - it works out of the box with your existing OpenAI API key!

## Installation

1. **Install the mem0ai dependency**:
   ```bash
   cd backend
   pip install mem0ai
   ```

2. **That's it!** The memory feature is now ready to use.

## How It Works

The memory feature automatically:

1. **Searches for relevant memories** when a user makes a request
2. **Enhances the system prompt** with relevant context from previous interactions
3. **Stores new memories** after each conversation
4. **Isolates memories by user** for privacy and security

## Configuration (Optional)

You can optionally configure the models used for memory processing by setting these environment variables:

```bash
# Optional - uses sensible defaults if not set
MEM0_LLM_MODEL=gpt-4o-mini                    # Model for memory processing
MEM0_EMBEDDER_MODEL=text-embedding-3-small   # Model for embeddings
```

## Testing

Test the memory integration:

```bash
cd backend
python scripts/test_memory.py
```

Run the demonstration:

```bash
python scripts/memory_example.py
```

## API Endpoints

The following memory management endpoints are now available:

### Search Memories
```http
GET /api/memory/search?query=your_search_query&limit=10&threshold=0.7
Authorization: Bearer <your_jwt_token>
```

### Get All User Memories
```http
GET /api/memory?limit=50
Authorization: Bearer <your_jwt_token>
```

### Add Memory
```http
POST /api/memory
Authorization: Bearer <your_jwt_token>
Content-Type: application/json

{
  "content": "I prefer using Python for backend development",
  "metadata": {"category": "preferences"}
}
```

### Delete Memory
```http
DELETE /api/memory/{memory_id}
Authorization: Bearer <your_jwt_token>
```

## Automatic Integration

The memory feature works automatically with your existing agent calls. When a user makes a request:

1. The system searches for relevant memories based on the user's query
2. Relevant memories are automatically added to the system prompt
3. The LLM receives enhanced context for more personalized responses
4. After the response, new memories are extracted and stored

## Example Usage

Here's what happens automatically when a user interacts with your agent:

```
User: "Help me with my Python web application"

System automatically:
1. Searches memories for "Python web application"
2. Finds: "User is building a FastAPI application with PostgreSQL"
3. Enhances system prompt with this context
4. LLM provides more relevant, personalized response
5. Stores new memories from the conversation
```

## Memory Types

The system automatically extracts and stores:

- **Technical preferences** (languages, frameworks, tools)
- **Project context** (current work, goals, requirements)
- **Personal information** (relevant details shared by user)
- **Interaction patterns** (how user typically works)

## Privacy & Security

- ✅ **User isolation**: Memories are strictly separated by user ID
- ✅ **No external services**: All data stays within your infrastructure
- ✅ **API-based management**: Users can view and delete their memories
- ✅ **Configurable relevance**: Only relevant memories are included

## Storage

mem0ai uses local file-based storage by default, which means:

- No external database required
- Data persists between application restarts
- Simple backup and migration (just copy the data files)
- No additional infrastructure costs

## Performance

- Memory operations are asynchronous and don't block LLM calls
- Memory storage happens in the background after responses
- Configurable limits and thresholds for optimal performance
- Relevance scoring ensures only useful memories are retrieved

## Troubleshooting

### Memory service not working
- Ensure OpenAI API key is configured
- Check application logs for initialization errors
- Verify file system write permissions

### No memories found
- Check that user_id is being passed correctly
- Verify memories were actually stored (check logs)
- Adjust relevance threshold if needed

### Memory storage failing
- Check OpenAI API key validity
- Verify sufficient disk space
- Check application logs for detailed errors

## Next Steps

1. **Install the dependency**: `pip install mem0ai`
2. **Test the feature**: Run the test scripts
3. **Monitor logs**: Watch for memory operations in your application logs
4. **Use the API**: Try the memory management endpoints
5. **Enjoy personalized responses**: Your agent now remembers user context!

The memory feature is now fully integrated and will automatically enhance your agent's responses with relevant user context from previous interactions.
