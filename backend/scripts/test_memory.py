#!/usr/bin/env python3
"""
Test script for memory integration with mem0ai.

This script tests the memory service functionality including:
- Adding memories
- Searching memories
- Memory integration with LLM calls
"""

import asyncio
import os
import sys

# Add the src directory to the path so we can import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from services.memory import memory_service
from services.llm import make_llm_api_call
from utils.logger import logger


async def test_memory_service():
    """Test the basic memory service functionality."""
    print("🧠 Testing Memory Service...")
    
    test_user_id = "test_user_123"
    
    # Test adding a memory
    print("\n1. Testing memory addition...")
    success = await memory_service.add_memory(
        content="I am a software engineer who loves Python and AI",
        user_id=test_user_id,
        metadata={"category": "personal_info"}
    )
    print(f"   Memory added: {success}")
    
    # Test adding another memory
    success = await memory_service.add_memory(
        content="I prefer working on backend systems and APIs",
        user_id=test_user_id,
        metadata={"category": "preferences"}
    )
    print(f"   Second memory added: {success}")
    
    # Test searching memories
    print("\n2. Testing memory search...")
    memories = await memory_service.search_memories(
        query="What do I like to work on?",
        user_id=test_user_id,
        limit=5,
        threshold=0.5
    )
    print(f"   Found {len(memories)} relevant memories:")
    for i, memory in enumerate(memories, 1):
        print(f"   {i}. {memory.get('content', '')[:100]}... (score: {memory.get('score', 0):.2f})")
    
    # Test getting all memories
    print("\n3. Testing get all memories...")
    all_memories = await memory_service.get_user_memories(
        user_id=test_user_id,
        limit=10
    )
    print(f"   Total memories for user: {len(all_memories)}")
    
    return test_user_id


async def test_llm_with_memory():
    """Test LLM integration with memory."""
    print("\n🤖 Testing LLM with Memory Integration...")
    
    test_user_id = "test_user_456"
    
    # Add some memories first
    print("\n1. Adding test memories...")
    await memory_service.add_memory(
        content="I am building a coding assistant application",
        user_id=test_user_id,
        metadata={"category": "project"}
    )
    
    await memory_service.add_memory(
        content="I prefer using FastAPI for backend development",
        user_id=test_user_id,
        metadata={"category": "tech_preference"}
    )
    
    # Test LLM call with memory integration
    print("\n2. Testing LLM call with memory...")
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "What kind of projects am I working on and what technologies do I prefer?"}
    ]
    
    try:
        # Note: This will only work if you have a valid OpenAI API key and the LLM service is properly configured
        response = await make_llm_api_call(
            messages=messages,
            model_name="gpt-4o-mini",
            user_id=test_user_id,
            stream=False
        )
        
        if hasattr(response, 'choices') and response.choices:
            response_content = response.choices[0].message.content
            print(f"   LLM Response: {response_content[:200]}...")
        else:
            print("   No response content received")
            
    except Exception as e:
        print(f"   LLM call failed (this is expected if API keys are not configured): {str(e)}")
    
    return test_user_id


async def cleanup_test_memories(user_ids):
    """Clean up test memories."""
    print("\n🧹 Cleaning up test memories...")
    
    for user_id in user_ids:
        try:
            memories = await memory_service.get_user_memories(user_id=user_id, limit=100)
            print(f"   Found {len(memories)} memories for user {user_id}")
            
            for memory in memories:
                memory_id = memory.get('id')
                if memory_id:
                    success = await memory_service.delete_memory(memory_id=memory_id, user_id=user_id)
                    if success:
                        print(f"   Deleted memory: {memory_id}")
                    
        except Exception as e:
            print(f"   Error cleaning up memories for {user_id}: {str(e)}")


async def main():
    """Main test function."""
    print("🚀 Starting Memory Integration Tests...")
    
    user_ids = []
    
    try:
        # Test basic memory service
        user_id_1 = await test_memory_service()
        user_ids.append(user_id_1)
        
        # Test LLM integration
        user_id_2 = await test_llm_with_memory()
        user_ids.append(user_id_2)
        
        print("\n✅ All tests completed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        logger.error(f"Memory test failed: {str(e)}", exc_info=True)
    
    finally:
        # Clean up test data
        if user_ids:
            await cleanup_test_memories(user_ids)
    
    print("\n🏁 Memory integration test finished!")


if __name__ == "__main__":
    asyncio.run(main())
