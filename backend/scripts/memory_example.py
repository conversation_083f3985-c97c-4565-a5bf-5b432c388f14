#!/usr/bin/env python3
"""
Example script demonstrating memory feature usage.

This script shows how the memory feature enhances agent interactions
by remembering user context and preferences across conversations.
"""

import asyncio
import os
import sys

# Add the src directory to the path so we can import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from services.memory import memory_service
from services.llm import make_llm_api_call
from utils.logger import logger


async def simulate_user_conversation():
    """Simulate a conversation with memory integration."""
    print("🎭 Simulating User Conversation with Memory...")
    
    user_id = "demo_user_001"
    
    # Conversation 1: User shares preferences
    print("\n📝 Conversation 1: User shares preferences")
    print("User: I'm a Python developer working on a web application using FastAPI and PostgreSQL")
    
    # Add this as a memory (normally this would be extracted automatically from conversation)
    await memory_service.add_memory(
        content="User is a Python developer working on a web application using FastAPI and PostgreSQL",
        user_id=user_id,
        metadata={"type": "tech_stack", "conversation": 1}
    )
    
    # Conversation 2: User asks for help
    print("\n💬 Conversation 2: User asks for help")
    print("User: Can you help me optimize my database queries?")
    
    # Search for relevant memories
    memories = await memory_service.search_memories(
        query="database queries optimization",
        user_id=user_id,
        limit=3,
        threshold=0.5
    )
    
    print(f"🧠 Found {len(memories)} relevant memories:")
    for memory in memories:
        print(f"   - {memory.get('content', '')}")
    
    # Simulate enhanced system prompt
    memory_context = ""
    if memories:
        memory_context = "\n\nRelevant context from previous interactions:\n"
        for memory in memories:
            memory_context += f"- {memory.get('content', '')}\n"
    
    enhanced_prompt = f"""You are a helpful coding assistant.{memory_context}
Use this context to provide more personalized and relevant responses."""
    
    print(f"\n🤖 Enhanced System Prompt:")
    print(enhanced_prompt)
    
    # Conversation 3: User asks about deployment
    print("\n🚀 Conversation 3: User asks about deployment")
    print("User: What's the best way to deploy my application?")
    
    # Add deployment preference memory
    await memory_service.add_memory(
        content="User is interested in deployment strategies for their web application",
        user_id=user_id,
        metadata={"type": "deployment", "conversation": 3}
    )
    
    # Search for all relevant context
    deployment_memories = await memory_service.search_memories(
        query="deployment web application FastAPI",
        user_id=user_id,
        limit=5,
        threshold=0.4
    )
    
    print(f"🧠 Found {len(deployment_memories)} relevant memories for deployment:")
    for memory in deployment_memories:
        print(f"   - {memory.get('content', '')} (score: {memory.get('score', 0):.2f})")
    
    return user_id


async def demonstrate_memory_persistence():
    """Demonstrate how memories persist across sessions."""
    print("\n🔄 Demonstrating Memory Persistence...")
    
    user_id = "persistent_user_002"
    
    # Session 1: Add some memories
    print("\n📅 Session 1: Adding user preferences")
    preferences = [
        "User prefers TypeScript over JavaScript for frontend development",
        "User likes to use Docker for containerization",
        "User works in a team of 5 developers",
        "User's favorite IDE is VS Code with Vim keybindings"
    ]
    
    for pref in preferences:
        await memory_service.add_memory(
            content=pref,
            user_id=user_id,
            metadata={"session": 1, "type": "preference"}
        )
        print(f"   ✅ Added: {pref}")
    
    # Session 2: Retrieve memories (simulating new session)
    print("\n📅 Session 2: Retrieving memories from previous session")
    all_memories = await memory_service.get_user_memories(
        user_id=user_id,
        limit=10
    )
    
    print(f"🧠 Retrieved {len(all_memories)} memories from previous session:")
    for i, memory in enumerate(all_memories, 1):
        content = memory.get('memory', memory.get('content', ''))
        print(f"   {i}. {content}")
    
    # Session 3: Search for specific information
    print("\n📅 Session 3: Searching for specific information")
    search_queries = [
        "What IDE does the user prefer?",
        "What frontend technology does the user like?",
        "How big is the user's team?"
    ]
    
    for query in search_queries:
        print(f"\n🔍 Query: {query}")
        results = await memory_service.search_memories(
            query=query,
            user_id=user_id,
            limit=2,
            threshold=0.6
        )
        
        if results:
            for result in results:
                content = result.get('memory', result.get('content', ''))
                score = result.get('score', 0)
                print(f"   📋 {content} (relevance: {score:.2f})")
        else:
            print("   ❌ No relevant memories found")
    
    return user_id


async def show_memory_categories():
    """Demonstrate different types of memories."""
    print("\n📂 Demonstrating Memory Categories...")
    
    user_id = "categorized_user_003"
    
    # Different categories of memories
    memory_categories = {
        "personal": [
            "User is based in San Francisco",
            "User has 5 years of experience in software development"
        ],
        "technical": [
            "User prefers microservices architecture",
            "User uses Redis for caching",
            "User follows TDD (Test-Driven Development)"
        ],
        "project": [
            "User is building an e-commerce platform",
            "User's project has a deadline in 3 months",
            "User's project needs to handle 10k concurrent users"
        ],
        "preferences": [
            "User prefers code reviews before merging",
            "User likes detailed documentation",
            "User prefers async/await over callbacks"
        ]
    }
    
    # Add categorized memories
    for category, memories in memory_categories.items():
        print(f"\n📁 Adding {category} memories:")
        for memory in memories:
            await memory_service.add_memory(
                content=memory,
                user_id=user_id,
                metadata={"category": category}
            )
            print(f"   ✅ {memory}")
    
    # Search within categories
    print(f"\n🔍 Searching across categories:")
    search_scenarios = [
        ("Tell me about the user's project", "project"),
        ("What are the user's technical preferences?", "technical"),
        ("What do we know about the user personally?", "personal")
    ]
    
    for query, expected_category in search_scenarios:
        print(f"\n❓ Query: {query}")
        results = await memory_service.search_memories(
            query=query,
            user_id=user_id,
            limit=3,
            threshold=0.5
        )
        
        for result in results:
            content = result.get('memory', result.get('content', ''))
            score = result.get('score', 0)
            metadata = result.get('metadata', {})
            category = metadata.get('category', 'unknown')
            print(f"   📋 [{category}] {content} (score: {score:.2f})")
    
    return user_id


async def cleanup_demo_data(user_ids):
    """Clean up demo data."""
    print("\n🧹 Cleaning up demo data...")
    
    for user_id in user_ids:
        try:
            memories = await memory_service.get_user_memories(user_id=user_id, limit=100)
            print(f"   Cleaning {len(memories)} memories for {user_id}")
            
            for memory in memories:
                memory_id = memory.get('id')
                if memory_id:
                    await memory_service.delete_memory(memory_id=memory_id, user_id=user_id)
                    
        except Exception as e:
            print(f"   Error cleaning up {user_id}: {str(e)}")


async def main():
    """Main demonstration function."""
    print("🚀 Memory Feature Demonstration")
    print("=" * 50)
    
    user_ids = []
    
    try:
        # Demonstrate conversation with memory
        user_id_1 = await simulate_user_conversation()
        user_ids.append(user_id_1)
        
        # Demonstrate memory persistence
        user_id_2 = await demonstrate_memory_persistence()
        user_ids.append(user_id_2)
        
        # Demonstrate memory categories
        user_id_3 = await show_memory_categories()
        user_ids.append(user_id_3)
        
        print("\n" + "=" * 50)
        print("✅ All demonstrations completed successfully!")
        print("\nKey Benefits Demonstrated:")
        print("🧠 Contextual memory search and retrieval")
        print("🔄 Memory persistence across sessions")
        print("📂 Organized memory categorization")
        print("🎯 Relevant memory matching with scoring")
        print("🤖 Enhanced AI responses with user context")
        
    except Exception as e:
        print(f"\n❌ Demonstration failed: {str(e)}")
        logger.error(f"Memory demonstration failed: {str(e)}", exc_info=True)
    
    finally:
        # Clean up demo data
        if user_ids:
            await cleanup_demo_data(user_ids)
    
    print("\n🏁 Memory feature demonstration finished!")


if __name__ == "__main__":
    asyncio.run(main())
