#!/usr/bin/env python3
"""
Test client for the agent API.
This demonstrates how to call the API from another project.
"""

import argparse

import requests


def main():
    """Run a test query against the agent API."""
    # Set up argument parser
    parser = argparse.ArgumentParser(description="Test the agent API.")
    parser.add_argument(
        "query",
        nargs="?",
        default="write a python script for hello world",
        help="The query to send to the agent",
    )
    parser.add_argument(
        "--model",
        default="anthropic/claude-3-7-sonnet-latest",
        help="The model to use (default: anthropic/claude-3-7-sonnet-latest)",
    )
    parser.add_argument("--context-manager", action="store_true", help="Enable context manager")
    parser.add_argument(
        "--url",
        default="http://localhost:8000",
        help="The URL of the agent API (default: http://localhost:8000)",
    )

    # Parse arguments
    args = parser.parse_args()

    # Get query from command line arguments or use default
    query = args.query
    base_url = args.url

    print(f"\n🤖 Testing agent API with query: '{query}'\n")

    try:
        # Prepare the request payload
        payload = {
            "query": query,
            "model": args.model,
            "enable_context_manager": args.context_manager,
        }

        # Make the request
        response = requests.post(f"{base_url}/query", json=payload)
        response.raise_for_status()

        result = response.json()

        print("\n🤖 Agent Response:\n")
        print(result["response"])

        print("\n✅ Test completed.")
        return 0
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback

        traceback.print_exc()
        return 1


if __name__ == "__main__":
    import sys

    sys.exit(main())
