import json
import re
from typing import Optional
from uuid import uuid4

from agent.run import run_agent
from agent.wrapper import run_agent_with_query
from agentpress.thread_manager import ThreadManager
from dotenv import load_dotenv
from sandbox.sandbox import create_sandbox
from utils import logger


async def test_agent():
    """Test function to run the agent with a sample query"""
    from agentpress.thread_manager import ThreadManager
    from services.supabase import DBConnection

    # Initialize ThreadManager
    thread_manager = ThreadManager()

    # Create a test thread directly with Postgres function
    client = await DBConnection().client

    try:
        # Get user's personal account
        # Try to get from RPC first
        account_result = await client.rpc("get_personal_account").execute()

        # If RPC fails, use a hardcoded account ID
        # You should replace this with your actual account ID from Supabase
        account_id = "f6aa2d32-452c-4ad8-b152-c7a6b9581cd4"

        print(f"Using account ID: {account_id}")

        if not account_id:
            print("Error: Could not get account ID")
            return

        # Find or create a test project in the user's account
        project_result = (
            await client.table("projects")
            .select("*")
            .eq("name", "test11")
            .eq("account_id", account_id)
            .execute()
        )

        if project_result.data and len(project_result.data) > 0:
            # Use existing test project
            project_id = project_result.data[0]["project_id"]
            print(f"\n🔄 Using existing test project: {project_id}")
        else:
            # Create new test project if none exists
            project_result = (
                await client.table("projects")
                .insert({"name": "test11", "account_id": account_id})
                .execute()
            )
            project_id = project_result.data[0]["project_id"]
            print(f"\n✨ Created new test project: {project_id}")

        # Create a sandbox for the project if it doesn't exist
        # First check if the project already has a sandbox
        project_details = (
            await client.table("projects").select("*").eq("project_id", project_id).execute()
        )
        if project_details.data and len(project_details.data) > 0:
            project_data = project_details.data[0]
            sandbox_info = project_data.get("sandbox", {})

            if not sandbox_info or not sandbox_info.get("id"):
                # Create a sandbox and update the project
                sandbox_pass = str(uuid4())
                sandbox = await create_sandbox(sandbox_pass)

                # Update the project with sandbox info
                await client.table("projects").update(
                    {"sandbox": {"id": sandbox.id, "password": sandbox_pass}}
                ).eq("project_id", project_id).execute()

                print(f"\n✨ Created and linked sandbox {sandbox.id} to project {project_id}")
            else:
                print(f"\n🔄 Project already has sandbox: {sandbox_info.get('id')}")

        # Create a thread for this project
        thread_result = (
            await client.table("threads")
            .insert({"project_id": project_id, "account_id": account_id})
            .execute()
        )
        thread_data = thread_result.data[0] if thread_result.data else None

        if not thread_data:
            print("Error: No thread data returned")
            return

        thread_id = thread_data["thread_id"]
    except Exception as e:
        print(f"Error setting up thread: {str(e)}")
        return

    print(f"\n🤖 Agent Thread Created: {thread_id}\n")

    # Interactive message input loop
    while True:
        # Get user input
        user_message = input("\n💬 Enter your message (or 'exit' to quit): ")
        if user_message.lower() == "exit":
            break

        if not user_message.strip():
            print("\n🔄 Running agent...\n")
            await process_agent_response(thread_id, project_id, thread_manager)
            continue

        # Add the user message to the thread
        await thread_manager.add_message(
            thread_id=thread_id,
            type="user",
            content={"role": "user", "content": user_message},
            is_llm_message=True,
        )

        print("\n🔄 Running agent...\n")
        print("DEBUG: About to call process_agent_response")
        try:
            await process_agent_response(thread_id, project_id, thread_manager)
            print("DEBUG: process_agent_response completed successfully")
        except Exception as e:
            print(f"ERROR in process_agent_response: {str(e)}")
            import traceback

            traceback.print_exc()

    print("\n👋 Test completed. Goodbye!")


async def process_agent_response(
    thread_id: str,
    project_id: str,
    thread_manager: ThreadManager,
    stream: bool = False,
    model_name: str = "anthropic/claude-3-7-sonnet-latest",
    enable_thinking: Optional[bool] = False,
    reasoning_effort: Optional[str] = "low",
    enable_context_manager: bool = True,
):
    """Process the streaming response from the agent."""
    chunk_counter = 0
    current_response = ""
    tool_usage_counter = 0  # Renamed from tool_call_counter as we track usage via status

    # We don't need to create a sandbox here as it should already be linked to the project
    # and the run_agent function will use that sandbox
    print("DEBUG: About to call run_agent")
    try:
        agent_response = run_agent(
            thread_id=thread_id,
            project_id=project_id,
            stream=stream,
            thread_manager=thread_manager,
            native_max_auto_continues=25,
            model_name=model_name,
            enable_thinking=enable_thinking,
            reasoning_effort=reasoning_effort,
            enable_context_manager=enable_context_manager,
        )
        print("DEBUG: Got response generator from run_agent")

        async for chunk in agent_response:
            chunk_counter += 1
            print(f"DEBUG CHUNK: {chunk}")  # Enable debugging

            if chunk.get("type") == "assistant":
                # Try parsing the content JSON
                try:
                    # Handle content as string or object
                    content = chunk.get("content", "{}")
                    if isinstance(content, str):
                        content_json = json.loads(content)
                    else:
                        content_json = content

                    actual_content = content_json.get("content", "")
                    # Print the actual assistant text content as it comes
                    if actual_content:
                        print(actual_content, end="", flush=True)
                        current_response += actual_content  # Accumulate only text part
                except json.JSONDecodeError:
                    # If content is not JSON (e.g., just a string chunk), print directly
                    raw_content = chunk.get("content", "")
                    print(raw_content, end="", flush=True)
                    current_response += raw_content
                except Exception as e:
                    print(f"\nError processing assistant chunk: {e}\n")

            elif chunk.get("type") == "tool":  # Updated from 'tool_result'
                # Add timestamp and format tool result nicely
                tool_name = "UnknownTool"  # Try to get from metadata if available
                result_content = "No content"

                # Parse metadata - handle both string and dict formats
                metadata = chunk.get("metadata", {})
                if isinstance(metadata, str):
                    try:
                        metadata = json.loads(metadata)
                    except json.JSONDecodeError:
                        metadata = {}

                linked_assistant_msg_id = metadata.get("assistant_message_id")
                parsing_details = metadata.get("parsing_details")
                if parsing_details:
                    tool_name = parsing_details.get(
                        "xml_tag_name", "UnknownTool"
                    )  # Get name from parsing details

                try:
                    # Content is a JSON string or object
                    content = chunk.get("content", "{}")
                    if isinstance(content, str):
                        content_json = json.loads(content)
                    else:
                        content_json = content

                    # The actual tool result is nested inside content.content
                    tool_result_str = content_json.get("content", "")
                    # Extract the actual tool result string (remove outer <tool_result> tag if present)
                    match = re.search(
                        rf"<{tool_name}>(.*?)</{tool_name}>", tool_result_str, re.DOTALL
                    )
                    if match:
                        result_content = match.group(1).strip()
                        # Try to parse the result string itself as JSON for pretty printing
                        try:
                            result_obj = json.loads(result_content)
                            result_content = json.dumps(result_obj, indent=2)
                        except json.JSONDecodeError:
                            # Keep as string if not JSON
                            pass
                    else:
                        # Fallback if tag extraction fails
                        result_content = tool_result_str

                except json.JSONDecodeError:
                    result_content = chunk.get("content", "Error parsing tool content")
                except Exception as e:
                    result_content = f"Error processing tool chunk: {e}"

                print(f"\n\n🛠️  TOOL RESULT [{tool_name}] → {result_content}")

            elif chunk.get("type") == "status":
                # Log tool status changes
                try:
                    # Handle content as string or object
                    status_content = chunk.get("content", "{}")
                    if isinstance(status_content, str):
                        status_content = json.loads(status_content)

                    status_type = status_content.get("status_type")
                    function_name = status_content.get("function_name", "")
                    xml_tag_name = status_content.get(
                        "xml_tag_name", ""
                    )  # Get XML tag if available
                    tool_name = xml_tag_name or function_name  # Prefer XML tag name

                    if status_type == "tool_started" and tool_name:
                        tool_usage_counter += 1
                        print(f"\n⏳ TOOL STARTING #{tool_usage_counter} [{tool_name}]")
                        print("  " + "-" * 40)
                        # Return to the current content display
                        if current_response:
                            print("\nContinuing response:", flush=True)
                            print(current_response, end="", flush=True)
                    elif status_type == "tool_completed" and tool_name:
                        status_emoji = "✅"
                        print(f"\n{status_emoji} TOOL COMPLETED: {tool_name}")
                    elif status_type == "finish":
                        finish_reason = status_content.get("finish_reason", "")
                        if finish_reason:
                            print(f"\n📌 Finished: {finish_reason}")
                    # else: # Print other status types if needed for debugging
                    #    print(f"\nℹ️ STATUS: {chunk.get('content')}")

                except json.JSONDecodeError:
                    print(f"\nWarning: Could not parse status content JSON: {chunk.get('content')}")
                except Exception as e:
                    print(f"\nError processing status chunk: {e}")
    except Exception as e:
        print(f"ERROR in run_agent: {str(e)}")
        import traceback

        traceback.print_exc()

        # Removed elif chunk.get('type') == 'tool_call': block

    # Update final message
    print(f"\n\n✅ Agent run completed with {tool_usage_counter} tool executions")

    # No need to clean up sandbox as we're using the one linked to the project


async def test_agent_wrapper():
    """Test function to demonstrate the agent wrapper with a single query"""
    # Configure any environment variables or setup needed for testing
    load_dotenv()  # Ensure environment variables are loaded

    # Example query
    query = "What is the current time?"

    print(f"\n🤖 Running agent with query: '{query}'\n")

    # Run the agent with the query and get the response
    response = await run_agent_with_query(
        query=query,
        # Optional parameters:
        # project_id=None,  # Will create a test project if not provided
        # account_id=None,  # Will try to get personal account if not provided
        model_name="anthropic/claude-3-7-sonnet-latest",
        enable_thinking=False,
        reasoning_effort="low",
        enable_context_manager=True,
        stream=False,  # Set to False to get the complete response at once
    )

    print("\n🤖 Agent Response:\n")
    print(response)

    print("\n✅ Test completed.")


if __name__ == "__main__":
    import asyncio

    # Configure any environment variables or setup needed for testing
    load_dotenv()  # Ensure environment variables are loaded

    # Choose which test to run
    # asyncio.run(test_agent())  # Run the interactive agent test
    asyncio.run(test_agent_wrapper())  # Run the agent wrapper test
