# fly.toml app configuration file generated for backend-staging-icy-mountain-363 on 2025-04-21T00:32:15+01:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'backend-staging-icy-mountain-363'
primary_region = 'cdg'

[build]
  dockerfile = 'Dockerfile'

[http_service]
  internal_port = 80
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  max_machines_count = 1
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1

[env]
  ENV_MODE = "staging"
