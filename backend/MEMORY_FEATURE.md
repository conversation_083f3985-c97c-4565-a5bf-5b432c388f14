# Memory Feature Integration with mem0ai

This document describes the memory feature integration using [mem0ai](https://github.com/mem0ai/mem0) that allows the agent to remember user interactions and provide more personalized responses.

## Overview

The memory feature enables the agent to:
- **Remember user preferences and context** across conversations
- **Search for relevant memories** when processing new requests
- **Automatically enhance system prompts** with relevant historical context
- **Store conversation memories** for future reference
- **Provide personalized responses** based on user history

## Architecture

### Components

1. **Memory Service** (`backend/src/services/memory.py`)
   - Handles all memory operations using mem0ai
   - Provides search, add, delete, and retrieval functionality
   - Manages memory lifecycle and error handling

2. **LLM Service Integration** (`backend/src/services/llm.py`)
   - Automatically enhances messages with relevant memories
   - Stores conversation memories after LLM responses
   - Seamlessly integrates with existing LLM call flow

3. **Memory API Endpoints** (`backend/src/agent/api.py`)
   - REST API for memory management
   - Allows users to view, search, and manage their memories

### Memory Flow

```
User Request → Search Relevant Memories → Enhance System Prompt → LLM Call → Store New Memories
```

1. **Memory Search**: When a user makes a request, the system searches for relevant memories
2. **Prompt Enhancement**: Relevant memories are added to the system prompt for context
3. **LLM Processing**: The LLM processes the request with memory context
4. **Memory Storage**: The conversation is analyzed and new memories are extracted and stored

## Configuration

Add the following optional environment variables to configure mem0ai:

```bash
# Memory (mem0ai) configuration (optional - uses defaults if not set)
MEM0_LLM_MODEL=gpt-4o-mini         # Model for memory processing
MEM0_EMBEDDER_MODEL=text-embedding-3-small  # Embedding model

# Required: OpenAI API key (already used by your application)
OPENAI_API_KEY=your_openai_api_key
```

## Installation

1. **Install mem0ai dependency**:
   ```bash
   pip install mem0ai
   ```

2. **That's it!** mem0ai works out of the box with no additional services required. It uses:
   - Your existing OpenAI API key for LLM and embedding operations
   - Built-in vector storage (no external database needed)
   - Local file-based persistence

## API Endpoints

### Search Memories
```http
GET /api/memory/search?query=your_search_query&limit=10&threshold=0.7
```

### Get All User Memories
```http
GET /api/memory?limit=50
```

### Add Memory
```http
POST /api/memory
Content-Type: application/json

{
  "content": "I prefer using Python for backend development",
  "metadata": {"category": "preferences"}
}
```

### Delete Memory
```http
DELETE /api/memory/{memory_id}
```

## Usage Examples

### Automatic Memory Integration

The memory feature works automatically with existing LLM calls. No code changes are needed for basic functionality:

```python
# This automatically includes relevant memories and stores new ones
response = await make_llm_api_call(
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "What are my coding preferences?"}
    ],
    model_name="gpt-4o-mini",
    user_id="user_123"  # User ID enables memory integration
)
```

### Manual Memory Management

```python
from services.memory import memory_service

# Add a memory
await memory_service.add_memory(
    content="User prefers React for frontend development",
    user_id="user_123",
    metadata={"category": "tech_preferences"}
)

# Search memories
memories = await memory_service.search_memories(
    query="frontend preferences",
    user_id="user_123",
    limit=5
)

# Get all memories
all_memories = await memory_service.get_user_memories(
    user_id="user_123",
    limit=100
)
```

## Testing

Run the memory integration test:

```bash
cd backend
python scripts/test_memory.py
```

This test will:
- Test basic memory operations (add, search, retrieve)
- Test LLM integration with memory
- Clean up test data

## Memory Types

The system automatically extracts and stores different types of memories:

1. **User Preferences**: Technology choices, working styles, preferences
2. **Project Context**: Current projects, goals, requirements
3. **Personal Information**: Relevant personal details shared by the user
4. **Interaction Patterns**: How the user typically interacts with the system

## Privacy and Security

- **User Isolation**: Memories are strictly isolated by user_id
- **Automatic Cleanup**: Memories can be deleted via API
- **Metadata Support**: Additional context can be stored with memories
- **Relevance Filtering**: Only relevant memories (above threshold) are included

## Performance Considerations

- **Async Operations**: All memory operations are asynchronous
- **Background Storage**: Memory storage happens in background to avoid blocking
- **Relevance Threshold**: Configurable threshold to control memory relevance
- **Limit Controls**: Configurable limits on memory search results

## Troubleshooting

### Common Issues

1. **Memory service not initialized**
   - Check OpenAI API key is configured
   - Verify environment variables
   - Check logs for initialization errors

2. **No memories found**
   - Verify user_id is being passed correctly
   - Check relevance threshold settings
   - Ensure memories were actually stored

3. **Memory storage failing**
   - Check OpenAI API key for memory processing
   - Verify file system write permissions
   - Check memory service logs

### Logs

Memory operations are logged with appropriate levels:
- `INFO`: Successful operations and counts
- `DEBUG`: Detailed operation information
- `WARNING`: Non-critical issues (fallback behavior)
- `ERROR`: Critical failures

## Future Enhancements

Potential improvements for the memory system:

1. **Memory Categories**: Automatic categorization of memories
2. **Memory Expiration**: Time-based memory cleanup
3. **Memory Sharing**: Shared memories across team members
4. **Memory Analytics**: Insights into memory usage patterns
5. **Memory Export**: Export user memories for backup/migration
