/**
 * Migration to update user triggers
 * This migration drops the trigger on auth.users and creates a new one on public.users
 */

-- 1. Drop the existing trigger on auth.users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- 2. Create a new function for the trigger on public.users
CREATE OR R<PERSON>LACE FUNCTION basejump.run_new_user_setup_public()
    RETURNS TRIGGER
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = public
AS $$
DECLARE
    first_account_id    uuid;
    generated_user_name text;
BEGIN
    -- Generate user name from email
    generated_user_name := split_part(new.email, '@', 1);

    -- Create the new user's personal account
    INSERT INTO basejump.accounts (name, primary_owner_user_id, personal_account, id)
    VALUES (generated_user_name, NEW.id, true, NEW.id)
    RETURNING id INTO first_account_id;

    RETURN NEW;
END;
$$;

-- 3. Create a new trigger on public.users
CREATE TRIGGER on_user_created
    AFTER INSERT
    ON public.users
    FOR EACH ROW
EXECUTE PROCEDURE basejump.run_new_user_setup_public();

-- Add comment to document the changes
COMMENT ON FUNCTION basejump.run_new_user_setup_public() IS 'Sets up a new user with a personal account when they are created in public.users';
