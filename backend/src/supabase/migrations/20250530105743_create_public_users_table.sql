/**
 * Migration to create a users table in the public schema
 * This table will replace references to auth.users
 */

-- Create new uid function in public schema
CREATE OR REPLACE FUNCTION public.uid()
RETURNS uuid
LANGUAGE sql
STABLE
AS $$
  SELECT
  (NULLIF(current_setting('request.jwt.claims', true), '')::jsonb ->> 'custom:ninja_user_id')::uuid;
$$;

-- <PERSON> execute permission for everyone
GRANT EXECUTE ON FUNCTION public.uid() TO PUBLIC;

-- Create the users table in the public schema
CREATE TABLE public.users (
    id uuid NOT NULL PRIMARY KEY,
    aud character varying(255),
    role character varying(255),
    email character varying(255),
    name character varying(255),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone null
);

-- Add indexes for better performance
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_created_at ON public.users(created_at);

-- Enable RLS on the users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Add RLS policies for users to manage their own data
CREATE POLICY users_allows_own_data ON public.users FOR ALL
    USING (id = public.uid());

-- Grant permissions
GRANT ALL ON public.users TO service_role;

-- Add comment to document the table
COMMENT ON TABLE public.users IS 'Replacement for auth.users references';
