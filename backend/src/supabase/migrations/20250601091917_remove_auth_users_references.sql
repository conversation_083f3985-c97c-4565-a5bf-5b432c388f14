/**
 * Migration to remove all references to auth.users
 * This migration modifies functions, triggers, and queries that reference auth.users
 */

-- Replace auth.uid() in basejump functions

-- Update add_current_user_to_new_account function
CREATE OR REPLACE FUNCTION basejump.add_current_user_to_new_account()
    RETURNS TRIGGER
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = public
AS $$
BEGIN
    IF new.primary_owner_user_id = public.uid() THEN
        INSERT INTO basejump.account_user (account_id, user_id, account_role)
        VALUES (NEW.id, public.uid(), 'owner');
    END IF;
    RETURN NEW;
END;
$$;

-- Update has_role_on_account function
CREATE OR REPLACE FUNCTION basejump.has_role_on_account(account_id uuid, account_role basejump.account_role DEFAULT NULL)
    RETURNS boolean
    LANGUAGE sql
    SECURITY DEFINER
    SET search_path = public
AS $$
SELECT EXISTS(
    SELECT 1
    FROM basejump.account_user wu
    WHERE wu.user_id = public.uid()
      AND wu.account_id = has_role_on_account.account_id
      AND (
            wu.account_role = has_role_on_account.account_role
            OR has_role_on_account.account_role IS NULL
        )
);
$$;

-- Update get_accounts_with_role function
CREATE OR REPLACE FUNCTION basejump.get_accounts_with_role(passed_in_role basejump.account_role DEFAULT NULL)
    RETURNS SETOF uuid
    LANGUAGE sql
    SECURITY DEFINER
    SET search_path = public
AS $$
SELECT account_id
FROM basejump.account_user wu
WHERE wu.user_id = public.uid()
  AND (
        wu.account_role = passed_in_role
        OR passed_in_role IS NULL
    );
$$;

-- Update current_user_account_role function
CREATE OR REPLACE FUNCTION public.current_user_account_role(account_id uuid)
    RETURNS jsonb
    LANGUAGE sql
    SECURITY DEFINER
    SET search_path = public
AS $$
SELECT jsonb_build_object(
               'user_id', public.uid(),
               'account_role', wu.account_role,
               'is_primary_owner', a.primary_owner_user_id = public.uid()
           )
FROM basejump.accounts a
         LEFT JOIN basejump.account_user wu
                   ON wu.account_id = a.id AND wu.user_id = public.uid()
WHERE a.id = current_user_account_role.account_id;
$$;

-- Update any other functions that use auth.uid()
CREATE OR REPLACE FUNCTION basejump.get_account_user_details(account_id uuid)
    RETURNS jsonb
    LANGUAGE sql
    SECURITY DEFINER
    SET search_path = public
AS $$
SELECT jsonb_build_object(
               'user_id', public.uid(),
               'account_id', account_id,
               'account_role', wu.account_role,
               'is_primary_owner', a.primary_owner_user_id = public.uid()
           )
FROM basejump.accounts a
         LEFT JOIN basejump.account_user wu
                   ON wu.account_id = a.id AND wu.user_id = public.uid()
WHERE a.id = get_account_user_details.account_id;
$$;

-- Add comment to document the changes
COMMENT ON FUNCTION public.uid() IS 'Replacement for auth.uid() that uses custom:ninja_user_id claim';

-- Update get_account_members function to not join with auth.users
CREATE OR REPLACE FUNCTION public.get_account_members(account_id uuid, results_limit integer DEFAULT 50, results_offset integer DEFAULT 0)
    RETURNS json
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = basejump
AS $$
BEGIN
    -- Only account owners can access this function
    IF (SELECT public.current_user_account_role(get_account_members.account_id) ->> 'account_role' <> 'owner') THEN
        RAISE EXCEPTION 'Only account owners can access this function';
    END IF;

    RETURN (SELECT json_agg(
                        json_build_object(
                            'user_id', wu.user_id,
                            'account_role', wu.account_role,
                            'name', p.name,
                            'is_primary_owner', a.primary_owner_user_id = wu.user_id
                        )
                    )
            FROM basejump.account_user wu
                JOIN basejump.accounts a ON a.id = wu.account_id
                JOIN basejump.accounts p ON p.primary_owner_user_id = wu.user_id AND p.personal_account = true
            WHERE wu.account_id = get_account_members.account_id
            LIMIT COALESCE(get_account_members.results_limit, 50) OFFSET COALESCE(get_account_members.results_offset, 0));
END;
$$;

-- Update get_account_billing_status function to not join with auth.users
CREATE OR REPLACE FUNCTION public.get_account_billing_status(account_id uuid)
    RETURNS jsonb
    SECURITY DEFINER
    SET search_path = public, basejump
AS $$
DECLARE
    result      jsonb;
    role_result jsonb;
BEGIN
    SELECT public.current_user_account_role(get_account_billing_status.account_id) INTO role_result;

    SELECT jsonb_build_object(
               'account_id', get_account_billing_status.account_id,
               'billing_subscription_id', s.id,
               'billing_enabled', CASE
                                      WHEN a.personal_account = true THEN config.enable_personal_account_billing
                                      ELSE config.enable_team_account_billing END,
               'billing_status', s.status,
               'billing_customer_id', c.id,
               'billing_provider', config.billing_provider,
               'billing_email', c.email
           )
    INTO result
    FROM basejump.accounts a
             LEFT JOIN basejump.billing_subscriptions s ON s.account_id = a.id
             LEFT JOIN basejump.billing_customers c ON c.account_id = COALESCE(s.account_id, a.id)
             JOIN basejump.config config ON true
    WHERE a.id = get_account_billing_status.account_id
    ORDER BY s.created DESC
    LIMIT 1;

    RETURN result || role_result;
END;
$$ LANGUAGE plpgsql;

-- Add comment to document the changes
COMMENT ON FUNCTION public.get_account_members(uuid, integer, integer) IS 'Modified to remove direct references to auth.users';
COMMENT ON FUNCTION public.get_account_billing_status(uuid) IS 'Modified to remove direct references to auth.users';


-- Update the get_accounts function to use public.uid() instead of auth.uid()
CREATE OR REPLACE FUNCTION public.get_accounts()
    RETURNS json
    LANGUAGE sql
AS $$
SELECT COALESCE(json_agg(
                        json_build_object(
                                'account_id', wu.account_id,
                                'account_role', wu.account_role,
                                'is_primary_owner', a.primary_owner_user_id = public.uid(),
                                'name', a.name,
                                'slug', a.slug,
                                'personal_account', a.personal_account,
                                'created_at', a.created_at,
                                'updated_at', a.updated_at
                            )
                    ), '[]'::json)
FROM basejump.account_user wu
         JOIN basejump.accounts a ON a.id = wu.account_id
WHERE wu.user_id = public.uid();
$$;

-- Add comment to document the changes
COMMENT ON FUNCTION public.get_accounts() IS 'Updated to use public.uid() instead of auth.uid()';

-- Update the get_personal_account function to use public.uid() instead of auth.uid()
CREATE OR REPLACE FUNCTION public.get_personal_account()
    RETURNS json
    LANGUAGE plpgsql
AS $$
BEGIN
    RETURN public.get_account(public.uid());
END;
$$;

-- Add comment to document the changes
COMMENT ON FUNCTION public.get_personal_account() IS 'Updated to use public.uid() instead of auth.uid()';

-- Update the update_account_user_role function to use public.uid() instead of auth.uid()
CREATE OR REPLACE FUNCTION public.update_account_user_role(account_id uuid, user_id uuid,
                                                         new_account_role basejump.account_role,
                                                         make_primary_owner boolean default false)
    RETURNS void
    SECURITY DEFINER
    SET search_path = public
    LANGUAGE plpgsql
AS $$
DECLARE
    is_account_owner         boolean;
    is_account_primary_owner boolean;
    changing_primary_owner   boolean;
BEGIN
    -- check if the user is an owner, and if they are, allow them to update the role
    SELECT basejump.has_role_on_account(update_account_user_role.account_id, 'owner') INTO is_account_owner;

    IF NOT is_account_owner THEN
        RAISE EXCEPTION 'You must be an owner of the account to update a users role';
    END IF;

    -- check if the user being changed is the primary owner, if so its not allowed
    SELECT primary_owner_user_id = public.uid(), primary_owner_user_id = update_account_user_role.user_id
    INTO is_account_primary_owner, changing_primary_owner
    FROM basejump.accounts
    WHERE id = update_account_user_role.account_id;

    IF changing_primary_owner = true AND is_account_primary_owner = false THEN
        RAISE EXCEPTION 'You must be the primary owner of the account to change the primary owner';
    END IF;

    UPDATE basejump.account_user au
    SET account_role = new_account_role
    WHERE au.account_id = update_account_user_role.account_id
      AND au.user_id = update_account_user_role.user_id;

    IF make_primary_owner = true THEN
        -- first we see if the current user is the owner, only they can do this
        IF is_account_primary_owner = false THEN
            RAISE EXCEPTION 'You must be the primary owner of the account to change the primary owner';
        END IF;

        UPDATE basejump.accounts
        SET primary_owner_user_id = update_account_user_role.user_id
        WHERE id = update_account_user_role.account_id;
    END IF;
END;
$$;

-- Add comment to document the changes
COMMENT ON FUNCTION public.update_account_user_role(uuid, uuid, basejump.account_role, boolean) IS 'Updated to use public.uid() instead of auth.uid()';

-- Update the trigger_set_invitation_details function to use public.uid() instead of auth.uid()
CREATE OR REPLACE FUNCTION basejump.trigger_set_invitation_details()
    RETURNS TRIGGER AS
$$
BEGIN
    NEW.invited_by_user_id = public.uid();
    NEW.account_name = (SELECT name FROM basejump.accounts WHERE id = NEW.account_id);
    RETURN NEW;
END
$$ LANGUAGE plpgsql;

-- Add comment to document the changes
COMMENT ON FUNCTION basejump.trigger_set_invitation_details() IS 'Updated to use public.uid() instead of auth.uid()';

-- Update the accept_invitation function to use public.uid() instead of auth.uid()
CREATE OR REPLACE FUNCTION public.accept_invitation(lookup_invitation_token text)
    RETURNS jsonb
    LANGUAGE plpgsql
    SECURITY DEFINER SET search_path = public, basejump
AS $$
DECLARE
    lookup_account_id       uuid;
    DECLARE new_member_role basejump.account_role;
    lookup_account_slug     text;
BEGIN
    SELECT i.account_id, i.account_role, a.slug
    INTO lookup_account_id, new_member_role, lookup_account_slug
    FROM basejump.invitations i
             JOIN basejump.accounts a ON a.id = i.account_id
    WHERE i.token = lookup_invitation_token
      AND i.created_at > now() - interval '24 hours';

    IF lookup_account_id IS NULL THEN
        RAISE EXCEPTION 'Invitation not found';
    END IF;

    IF lookup_account_id IS NOT NULL THEN
        -- we've validated the token is real, so grant the user access
        INSERT INTO basejump.account_user (account_id, user_id, account_role)
        VALUES (lookup_account_id, public.uid(), new_member_role);
        -- email types of invitations are only good for one usage
        DELETE FROM basejump.invitations WHERE token = lookup_invitation_token AND invitation_type = 'one_time';
    END IF;
    RETURN json_build_object('account_id', lookup_account_id, 'account_role', new_member_role, 'slug',
                             lookup_account_slug);
EXCEPTION
    WHEN unique_violation THEN
        RAISE EXCEPTION 'You are already a member of this account';
END;
$$;

-- Add comment to document the changes
COMMENT ON FUNCTION public.accept_invitation(text) IS 'Updated to use public.uid() instead of auth.uid()';

-- Update the trigger_set_user_tracking function to use public.uid() instead of auth.uid()
CREATE OR REPLACE FUNCTION basejump.trigger_set_user_tracking()
    RETURNS TRIGGER AS
$$
BEGIN
    IF TG_OP = 'INSERT' THEN
        NEW.created_by = public.uid();
        NEW.updated_by = public.uid();
    ELSE
        NEW.updated_by = public.uid();
        NEW.created_by = OLD.created_by;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add comment to document the changes
COMMENT ON FUNCTION basejump.trigger_set_user_tracking() IS 'Updated to use public.uid() instead of auth.uid()';

-- Update the policy for users to view their own account_users
ALTER POLICY "users can view their own account_users" ON basejump.account_user
    USING (user_id = public.uid());

-- Update the policy for accounts viewable by primary owner
ALTER POLICY "Accounts are viewable by primary owner" ON basejump.accounts
    USING (primary_owner_user_id = public.uid());
