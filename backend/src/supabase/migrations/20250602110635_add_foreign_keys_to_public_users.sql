/**
 * Migration to add foreign key constraints to public.users
 * This migration recreates the foreign keys that were previously on auth.users
 */

-- Add foreign key constraints to basejump.accounts
ALTER TABLE basejump.accounts
    ADD CONSTRAINT accounts_primary_owner_user_id_fkey
        FOREIGN KEY (primary_owner_user_id) REFERENCES public.users(id),
    ADD CONSTRAINT accounts_created_by_fkey
        FOREIGN KEY (created_by) REFERENCES public.users(id),
    ADD CONSTRAINT accounts_updated_by_fkey
        FOREIGN KEY (updated_by) REFERENCES public.users(id);

-- Add foreign key constraint to basejump.account_user
ALTER TABLE basejump.account_user
    ADD CONSTRAINT account_user_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- Add foreign key constraint to basejump.invitations
ALTER TABLE basejump.invitations
    ADD CONSTRAINT invitations_invited_by_user_id_fkey
        FOREIGN KEY (invited_by_user_id) REFERENCES public.users(id);

-- Add comment to document the changes
COMMENT ON TABLE basejump.accounts IS 'Foreign key constraints now reference public.users instead of auth.users';
COMMENT ON TABLE basejump.account_user IS 'Foreign key constraint now references public.users instead of auth.users';
COMMENT ON TABLE basejump.invitations IS 'Foreign key constraint now references public.users instead of auth.users';
