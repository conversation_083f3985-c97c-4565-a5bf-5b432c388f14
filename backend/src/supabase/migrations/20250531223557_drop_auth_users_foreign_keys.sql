/**
 * Migration to drop all foreign keys on auth.users
 * This migration removes foreign key constraints that reference auth.users
 */

-- Drop foreign key constraints on basejump.accounts
ALTER TABLE basejump.accounts
    DROP CONSTRAINT IF EXISTS accounts_primary_owner_user_id_fkey,
    DROP CONSTRAINT IF EXISTS accounts_created_by_fkey,
    DROP CONSTRAINT IF EXISTS accounts_updated_by_fkey;

-- Drop foreign key constraint on basejump.account_user
ALTER TABLE basejump.account_user
    DROP CONSTRAINT IF EXISTS account_user_user_id_fkey;

-- Drop foreign key constraint on basejump.invitations
ALTER TABLE basejump.invitations
    DROP CONSTRAINT IF EXISTS invitations_invited_by_user_id_fkey;

-- Add comment to document the change
COMMENT ON TABLE basejump.accounts IS 'Foreign key constraints to auth.users have been removed';
COMMENT ON TABLE basejump.account_user IS 'Foreign key constraint to auth.users has been removed';
COMMENT ON TABLE basejump.invitations IS 'Foreign key constraint to auth.users has been removed';
