/**
 * Migration to copy users from auth.users to public.users
 * This migration copies relevant user data from the auth schema to the public schema
 */

-- Copy data from auth.users to public.users
-- We're copying only the relevant fields: id, aud, role, email, and timestamps
INSERT INTO public.users (id, aud, role, email, name, created_at, updated_at, deleted_at)
SELECT
    id,
    aud,
    role,
    email,
    -- Always set name to null as requested
    NULL as name,
    COALESCE(created_at, NOW()) as created_at,
    COALESCE(updated_at, NOW()) as updated_at,
    deleted_at
FROM auth.users;

-- Add comment to document the migration
COMMENT ON TABLE public.users IS 'User data migrated from auth.users';

-- Create a trigger function to add new auth.users to public.users
CREATE OR REPLACE FUNCTION public.sync_auth_users_to_public_users()
RETURNS TRIGGER AS $$
BEGIN
    -- Only handle INSERT operations
    INSERT INTO public.users (id, aud, role, email, name, created_at, updated_at, deleted_at)
    VALUES (
        NEW.id,
        NEW.aud,
        NEW.role,
        NEW.email,
        NULL,
        COALESCE(NEW.created_at, NOW()),
        COALESCE(NEW.updated_at, NOW()),
        NEW.deleted_at
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create only the insert trigger to add new auth.users to public.users
CREATE TRIGGER sync_auth_users_insert
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION public.sync_auth_users_to_public_users();

-- Add comment to document the trigger function
COMMENT ON FUNCTION public.sync_auth_users_to_public_users() IS 'Adds new auth.users to public.users';
