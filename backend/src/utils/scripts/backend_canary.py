#!/usr/bin/env python
import asyncio
import json
import os
import time
from typing import Any

import httpx
import requests
from dotenv import load_dotenv
from ninja_common.metrics import NinjaMetricsCollector
from pycognito import Cognito
from utils.config import config
from utils.constants import SUPER_AGENT_SERVICE_NAME
from utils.logger import logger

load_dotenv(override=True)


def get_cognito_token(user_pool_id: str, client_id: str, username: str, password: str):
    cognito = Cognito(user_pool_id=user_pool_id, client_id=client_id, username=username)
    # Authenticate using SRP (Secure Remote Password)
    cognito.authenticate(password=password)
    return cognito.access_token


async def main():
    """Main function to run the script."""
    with NinjaMetricsCollector(
        service_name=SUPER_AGENT_SERVICE_NAME, dimensions=[{"CanaryType": "BackendCanary"}]
    ) as metrics:
        API_URL = os.environ["SUPERNINJA_API_URL"]
        NINJA_USER_POOL_ID = os.environ["NINJA_USER_POOL_ID"]
        NINJA_USER_POOL_CLIENT_ID = os.environ["NINJA_USER_POOL_CLIENT_ID"]
        CANARY_USERNAME = os.environ["SUPERNINJA_CANARY_USERNAME"]
        CANARY_USER_PASSWORD = os.environ["SUPERNINJA_CANARY_USER_PASSWORD"]
        MODEL_TO_USE = config.MODEL_TO_USE

        jwt_token = get_cognito_token(
            user_pool_id=NINJA_USER_POOL_ID,
            client_id=NINJA_USER_POOL_CLIENT_ID,
            username=CANARY_USERNAME,
            password=CANARY_USER_PASSWORD,
        )
        response = call_api_url(API_URL, MODEL_TO_USE, jwt_token)
        process(response.json(), API_URL, jwt_token)
        metrics.count("CanarySuccess")


def call_api_url(api_url: str, model_to_use: str, jwt_token: str) -> httpx.Response:
    data = {
        "prompt": "What days are usually included in the weekend?",
        "model_name": model_to_use,
        "enable_thinking": "False",
        "stream": "False",
    }
    # Always use the file-supporting API endpoint
    response = requests.post(
        f"{api_url}/agent/initiate",
        headers={"Authorization": f"Bearer {jwt_token}"},
        data=data,
    )
    logger.info(f"Agent initiation response: {response.text}")
    response.raise_for_status()  # Raise an exception for 4XX/5XX responses
    return response


def process(api_response: dict[str, Any], api_url: str, jwt_token: str):
    # Process the response from the API
    agent_run_id = api_response.get("agent_run_id")

    # Wait for the agent to process
    logger.info("Waiting for agent to process...")
    time.sleep(5)

    # Get agent run status
    status_response = requests.get(
        f"{api_url}/agent-run/{agent_run_id}",
        headers={"Authorization": f"Bearer {jwt_token}"},
    )

    status_data = status_response.json()
    status = status_data.get("status")
    logger.info(f"Current status: {status}")

    # Always use the stream endpoint to get responses
    logger.info("Fetching agent responses from stream endpoint...")
    stream_response = requests.get(
        f"{api_url}/agent-run/{agent_run_id}/stream",
        headers={"Authorization": f"Bearer {jwt_token}"},
    )

    responses = []
    if stream_response.status_code == 200:
        # Parse the SSE stream format
        for line in stream_response.text.split("\n"):
            if line.startswith("data: "):
                try:
                    data = json.loads(line[6:])  # Remove 'data: ' prefix
                    responses.append(data)
                except json.JSONDecodeError:
                    pass

    # Extract the final message content
    raw_output = ""
    if responses:
        assistant_messages = [r for r in responses if r.get("type") == "assistant"]
        if assistant_messages:
            last_message = assistant_messages[-1]
            try:
                content_json = json.loads(last_message.get("content", "{}"))
                raw_output = content_json.get("content", "")
            except json.JSONDecodeError:
                raw_output = last_message.get("content", "")
            # logger.info for debugging
            logger.info(f"\nLast Assistant Message Content: {raw_output}")


if __name__ == "__main__":
    asyncio.run(main())
