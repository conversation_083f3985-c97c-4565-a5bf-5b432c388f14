"""
Configuration management.

This module provides a centralized way to access configuration settings and
environment variables across the application. It supports different environment
modes (development, staging, production) and provides validation for required
values.

Usage:
    from utils.config import config

    # Access configuration values
    api_key = config.OPENAI_API_KEY
    env_mode = config.ENV_MODE
"""
import json
import os
from enum import Enum

from dotenv import load_dotenv
from ninja_common.stages.exports import StageSecrets
from pydantic import field_validator, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from utils.logger import logger

# We expose a bunch of secrets as a map inside an environment variable
SUPER_AGENT_SECRETS_ENV_VAR = os.getenv(StageSecrets.SUPER_AGENT_API_KEY.name, None)


class EnvMode(Enum):
    """Environment mode enumeration."""

    LOCAL = "local"
    STAGING = "staging"
    PRODUCTION = "production"


# TODO: Cut down this config with only dependencies we need
class Configuration(BaseSettings):
    """
    Centralized configuration for AgentPress backend.

    This class loads environment variables and provides type checking and validation.
    Default values can be specified for optional configuration items.
    """

    model_config = SettingsConfigDict(extra="ignore")

    # Environment mode
    ENV_MODE: EnvMode = EnvMode.LOCAL

    # Subscription tier IDs - Production
    STRIPE_FREE_TIER_ID_PROD: str = "price_1RILb4G6l1KZGqIrK4QLrx9i"
    STRIPE_TIER_2_20_ID_PROD: str = "price_1RILb4G6l1KZGqIrhomjgDnO"
    STRIPE_TIER_6_50_ID_PROD: str = "price_1RILb4G6l1KZGqIr5q0sybWn"
    STRIPE_TIER_12_100_ID_PROD: str = "price_1RILb4G6l1KZGqIr5Y20ZLHm"
    STRIPE_TIER_25_200_ID_PROD: str = "price_1RILb4G6l1KZGqIrGAD8rNjb"
    STRIPE_TIER_50_400_ID_PROD: str = "price_1RILb4G6l1KZGqIruNBUMTF1"
    STRIPE_TIER_125_800_ID_PROD: str = "price_1RILb3G6l1KZGqIrbJA766tN"
    STRIPE_TIER_200_1000_ID_PROD: str = "price_1RILb3G6l1KZGqIrmauYPOiN"

    # Subscription tier IDs - Staging
    STRIPE_FREE_TIER_ID_STAGING: str = "price_1RIGvuG6l1KZGqIrw14abxeL"
    STRIPE_TIER_2_20_ID_STAGING: str = "price_1RIGvuG6l1KZGqIrCRu0E4Gi"
    STRIPE_TIER_6_50_ID_STAGING: str = "price_1RIGvuG6l1KZGqIrvjlz5p5V"
    STRIPE_TIER_12_100_ID_STAGING: str = "price_1RIGvuG6l1KZGqIrT6UfgblC"
    STRIPE_TIER_25_200_ID_STAGING: str = "price_1RIGvuG6l1KZGqIrOVLKlOMj"
    STRIPE_TIER_50_400_ID_STAGING: str = "price_1RIKNgG6l1KZGqIrvsat5PW7"
    STRIPE_TIER_125_800_ID_STAGING: str = "price_1RIKNrG6l1KZGqIrjKT0yGvI"
    STRIPE_TIER_200_1000_ID_STAGING: str = "price_1RIKQ2G6l1KZGqIrum9n8SI7"

    # Computed subscription tier IDs based on environment
    @property
    def STRIPE_FREE_TIER_ID(self) -> str:
        if self.ENV_MODE == EnvMode.STAGING:
            return self.STRIPE_FREE_TIER_ID_STAGING
        return self.STRIPE_FREE_TIER_ID_PROD

    @property
    def STRIPE_TIER_2_20_ID(self) -> str:
        if self.ENV_MODE == EnvMode.STAGING:
            return self.STRIPE_TIER_2_20_ID_STAGING
        return self.STRIPE_TIER_2_20_ID_PROD

    @property
    def STRIPE_TIER_6_50_ID(self) -> str:
        if self.ENV_MODE == EnvMode.STAGING:
            return self.STRIPE_TIER_6_50_ID_STAGING
        return self.STRIPE_TIER_6_50_ID_PROD

    @property
    def STRIPE_TIER_12_100_ID(self) -> str:
        if self.ENV_MODE == EnvMode.STAGING:
            return self.STRIPE_TIER_12_100_ID_STAGING
        return self.STRIPE_TIER_12_100_ID_PROD

    @property
    def STRIPE_TIER_25_200_ID(self) -> str:
        if self.ENV_MODE == EnvMode.STAGING:
            return self.STRIPE_TIER_25_200_ID_STAGING
        return self.STRIPE_TIER_25_200_ID_PROD

    @property
    def STRIPE_TIER_50_400_ID(self) -> str:
        if self.ENV_MODE == EnvMode.STAGING:
            return self.STRIPE_TIER_50_400_ID_STAGING
        return self.STRIPE_TIER_50_400_ID_PROD

    @property
    def STRIPE_TIER_125_800_ID(self) -> str:
        if self.ENV_MODE == EnvMode.STAGING:
            return self.STRIPE_TIER_125_800_ID_STAGING
        return self.STRIPE_TIER_125_800_ID_PROD

    @property
    def STRIPE_TIER_200_1000_ID(self) -> str:
        if self.ENV_MODE == EnvMode.STAGING:
            return self.STRIPE_TIER_200_1000_ID_STAGING
        return self.STRIPE_TIER_200_1000_ID_PROD

    # LLM API keys
    ANTHROPIC_API_KEY: str | None = None
    OPENAI_API_KEY: str | None = None
    OPENAI_API_BASE: str | None = None
    GROQ_API_KEY: str | None = None
    OPENROUTER_API_KEY: str | None = None
    OPENROUTER_API_BASE: str | None = "https://openrouter.ai/api/v1"
    OR_SITE_URL: str | None = "https://kortix.ai"
    OR_APP_NAME: str | None = "Kortix AI"

    # AWS Bedrock credentials
    AWS_ACCESS_KEY_ID: str | None = None
    AWS_SECRET_ACCESS_KEY: str | None = None
    AWS_REGION_NAME: str | None = None

    # Model configuration
    MODEL_TO_USE: str | None = "anthropic/claude-3-7-sonnet-latest"

    # Supabase configuration
    SUPABASE_URL: str | None = None
    SUPABASE_ANON_KEY: str | None = None
    SUPABASE_SERVICE_ROLE_KEY: str | None = None

    # Redis configuration
    REDIS_HOST: str | None = None
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: str | None = None
    REDIS_SSL: bool = True

    # Daytona sandbox configuration
    DAYTONA_API_KEY: str | None = None
    DAYTONA_SERVER_URL: str | None = None
    DAYTONA_TARGET: str | None = None

    # Search and other API keys
    TAVILY_API_KEY: str | None = None
    RAPID_API_KEY: str | None = None
    CLOUDFLARE_API_TOKEN: str | None = None
    FIRECRAWL_API_KEY: str | None = None
    FIRECRAWL_URL: str | None = "https://api.firecrawl.dev"

    # Memory (mem0ai) configuration
    MEM0_LLM_MODEL: str | None = "gpt-4o-mini"
    MEM0_EMBEDDER_MODEL: str | None = "text-embedding-3-small"

    # Website Deployment resources
    WEBSITE_DEPLOYMENT_BUCKET: str | None = None
    WEBSITE_DEPLOYMENT_CLOUDFRONT_ID: str | None = None
    WEBSITE_DEPLOYMENT_URL: str | None = None

    # Stripe configuration
    STRIPE_SECRET_KEY: str | None = None
    STRIPE_WEBHOOK_SECRET: str | None = None
    STRIPE_DEFAULT_PLAN_ID: str | None = None
    STRIPE_DEFAULT_TRIAL_DAYS: int = 14

    # Stripe Product IDs
    STRIPE_PRODUCT_ID_PROD: str = "prod_SCl7AQ2C8kK1CD"  # Production product ID
    STRIPE_PRODUCT_ID_STAGING: str = "prod_SCgIj3G7yPOAWY"  # Staging product ID

    # Sandbox image
    SANDBOX_IMAGE_NAME: str = "ninja/suna"
    SANDBOX_IMAGE_TAG: str = "0.1.4"

    @property
    def STRIPE_PRODUCT_ID(self) -> str:
        if self.ENV_MODE == EnvMode.STAGING:
            return self.STRIPE_PRODUCT_ID_STAGING
        return self.STRIPE_PRODUCT_ID_PROD

    @property
    def SANDBOX_IMAGE(self) -> str:
        return f"{self.SANDBOX_IMAGE_NAME}:{self.SANDBOX_IMAGE_TAG}"

    @model_validator(mode="before")
    def _load_env(cls, values):
        load_dotenv()

        if SUPER_AGENT_SECRETS_ENV_VAR:
            # Expand secret into env vars and put into configuration
            secrets = json.loads(SUPER_AGENT_SECRETS_ENV_VAR)
            for k, v in secrets.items():
                values[k] = os.environ[k] = str(v)

        return values

    @field_validator("ENV_MODE", mode="before")
    def _validate_env_mode(cls, value: str) -> EnvMode:
        try:
            return EnvMode(value)
        except ValueError:
            logger.warning(f"Invalid ENV_MODE: {value}, defaulting to LOCAL")
            return EnvMode.LOCAL


config = Configuration()
