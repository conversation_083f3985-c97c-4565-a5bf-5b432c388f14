from cachetools.func import ttl_cache
from ninja_common.constants import NINJA_CLIENT_CACHE_DURATION_SECONDS
from ninja_feedback_client.client import FeedbackApiAsyncClient
from services.config import FEEDBACK_SERVICE_BASE_URL


@ttl_cache(ttl=NINJA_CLIENT_CACHE_DURATION_SECONDS)
def get_feedback_service_client() -> FeedbackApiAsyncClient:
    return FeedbackApiAsyncClient(base_url=FEEDBACK_SERVICE_BASE_URL)
