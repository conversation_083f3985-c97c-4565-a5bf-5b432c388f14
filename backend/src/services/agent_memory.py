"""
Agent-level memory integration service.

This module provides memory functionality specifically for agent runs, allowing:
- Memory enhancement of system prompts for agent runs
- Storage of user request and agent response memories
- Integration with the agent run lifecycle
"""

import asyncio
from typing import Any, Dict, List, Optional

from services.memory import memory_service
from utils.logger import logger


async def enhance_system_prompt_with_memory(
    system_prompt: Dict[str, Any],
    user_query: str,
    user_id: str,
    thread_id: Optional[str] = None,
    limit: int = 5,
    threshold: float = 0.6
) -> Dict[str, Any]:
    """
    Enhance system prompt with relevant memories for an agent run.
    
    Args:
        system_prompt: The original system prompt message
        user_query: The user's query/request for memory search
        user_id: User ID to search memories for
        thread_id: Optional thread ID for context
        limit: Maximum number of memories to include
        threshold: Minimum relevance threshold (0-1)
        
    Returns:
        Enhanced system prompt with memory context
    """
    
    import pdb; pdb.set_trace()
    # Search for relevant memories
    relevant_memories = await memory_service.search_memories(
        query=user_query,
        user_id=user_id,
        limit=limit,
        threshold=threshold
    )
    
    if not relevant_memories:
        logger.debug(f"No relevant memories found for user {user_id}")
        return system_prompt
    
    # Format memories for inclusion in system prompt
    memory_context = "\n\n## Relevant User Context from Previous Interactions:\n"
    for i, memory in enumerate(relevant_memories, 1):
        memory_content = memory.get("content", memory.get("memory", ""))
        memory_score = memory.get("score", 0)
        memory_context += f"{i}. {memory_content} (relevance: {memory_score:.2f})\n"
    
    memory_context += "\nUse this context to provide more personalized and relevant responses.\n"
    
    # Enhance system prompt with memory context
    enhanced_prompt = system_prompt.copy()
    original_content = enhanced_prompt.get("content", "")
    
    if isinstance(original_content, str):
        enhanced_prompt["content"] = original_content + memory_context
    elif isinstance(original_content, list):
        # Handle list content format - add memory as new text block
        enhanced_content = original_content + [
            {"type": "text", "text": memory_context}
        ]
        enhanced_prompt["content"] = enhanced_content
    else:
        # If content format is unexpected, add as string
        enhanced_prompt["content"] = str(original_content) + memory_context
    
    logger.info(f"Enhanced system prompt with {len(relevant_memories)} relevant memories for user {user_id}")
    return enhanced_prompt
        


async def store_agent_run_memory(
    user_query: str,
    agent_response: str,
    user_id: str,
    thread_id: Optional[str] = None,
    model_name: Optional[str] = None,
    agent_run_id: Optional[str] = None
) -> bool:
    """Store memory from user request."""
    if user_id and user_query:
        return await memory_service.add_memory(
            content=user_query,
            user_id=user_id,
            metadata={"thread_id": thread_id, "type": "user_request"}
        )
    return False


def store_agent_run_memory_async(
    user_query: str,
    agent_response: str,
    user_id: str,
    thread_id: Optional[str] = None,
    model_name: Optional[str] = None,
    agent_run_id: Optional[str] = None
) -> None:
    """Store memory in background."""
    if user_id and user_query:
        asyncio.create_task(memory_service.add_memory(
            content=user_query,
            user_id=user_id,
            metadata={"thread_id": thread_id, "type": "user_request"}
        ))


def extract_user_query_from_messages(messages: List[Dict[str, Any]]) -> str:
    """
    Extract the user query from a list of messages.
    
    Args:
        messages: List of message dictionaries
        
    Returns:
        The user query string, or empty string if not found
    """
    try:
        # Look for the last user message
        for msg in reversed(messages):
            if msg.get("role") == "user":
                content = msg.get("content", "")
                if isinstance(content, str):
                    return content
                elif isinstance(content, list):
                    # Handle list content format
                    for item in content:
                        if isinstance(item, dict) and item.get("type") == "text":
                            return item.get("text", "")
        
        return ""
        
    except Exception as e:
        logger.error(f"Error extracting user query from messages: {str(e)}")
        return ""


def extract_agent_response_from_messages(messages: List[Dict[str, Any]]) -> str:
    """
    Extract the agent response from a list of messages.
    
    Args:
        messages: List of message dictionaries
        
    Returns:
        The agent response string, or empty string if not found
    """
    try:
        # Look for the last assistant message
        for msg in reversed(messages):
            if msg.get("role") == "assistant":
                content = msg.get("content", "")
                if isinstance(content, str):
                    return content
                elif isinstance(content, list):
                    # Handle list content format
                    for item in content:
                        if isinstance(item, dict) and item.get("type") == "text":
                            return item.get("text", "")
        
        return ""
        
    except Exception as e:
        logger.error(f"Error extracting agent response from messages: {str(e)}")
        return ""


async def get_user_memory_summary(user_id: str, limit: int = 10) -> str:
    """
    Get a summary of user's recent memories.
    
    Args:
        user_id: User ID to get memories for
        limit: Maximum number of memories to include
        
    Returns:
        A formatted summary of user memories
    """
    try:
        memories = await memory_service.get_user_memories(
            user_id=user_id,
            limit=limit
        )
        
        if not memories:
            return "No previous memories found for this user."
        
        summary = f"User has {len(memories)} stored memories:\n"
        for i, memory in enumerate(memories[:5], 1):  # Show top 5
            content = memory.get("memory", memory.get("content", ""))
            summary += f"{i}. {content[:100]}...\n"
        
        if len(memories) > 5:
            summary += f"... and {len(memories) - 5} more memories."
        
        return summary
        
    except Exception as e:
        logger.error(f"Error getting user memory summary: {str(e)}")
        return "Error retrieving user memories."
