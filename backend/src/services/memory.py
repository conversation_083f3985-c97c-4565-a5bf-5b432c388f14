"""
Memory service for agent using mem0ai.

This module provides memory functionality for the agent, allowing it to:
- Store and retrieve user-specific memories
- Search for relevant memories based on context
- Add new memories from user interactions and LLM responses
- Manage memory lifecycle across user sessions
"""

import os
from typing import Any, Dict, List, Optional

from mem0 import Memory
from utils.config import config
from utils.logger import logger


class MemoryService:
    """Service for managing agent memory using mem0ai."""

    def __init__(self):
        """Initialize the memory service with mem0ai configuration."""
        self._memory_client = None
        self._initialize_client()

    def _initialize_client(self) -> None:
        """Initialize the mem0 client with configuration."""
        try:
            # Configure mem0 with environment variables or defaults
            mem0_config = {
                "vector_store": {
                    "provider": "qdrant",
                    "config": {
                        "host": getattr(config, "MEM0_QDRANT_HOST", "localhost"),
                        "port": getattr(config, "MEM0_QDRANT_PORT", 6333),
                        "api_key": getattr(config, "MEM0_QDRANT_API_KEY", None),
                    }
                },
                "llm": {
                    "provider": "openai",
                    "config": {
                        "model": getattr(config, "MEM0_LLM_MODEL", "gpt-4o-mini"),
                        "api_key": getattr(config, "OPENAI_API_KEY", None),
                    }
                },
                "embedder": {
                    "provider": "openai",
                    "config": {
                        "model": getattr(config, "MEM0_EMBEDDER_MODEL", "text-embedding-3-small"),
                        "api_key": getattr(config, "OPENAI_API_KEY", None),
                    }
                }
            }
            
            self._memory_client = Memory.from_config(mem0_config)
            logger.info("Memory service initialized successfully with mem0ai")
            
        except Exception as e:
            logger.error(f"Failed to initialize memory service: {str(e)}")
            # Initialize with default configuration as fallback
            try:
                self._memory_client = Memory()
                logger.warning("Memory service initialized with default configuration")
            except Exception as fallback_error:
                logger.error(f"Failed to initialize memory service with defaults: {str(fallback_error)}")
                self._memory_client = None

    async def search_memories(
        self, 
        query: str, 
        user_id: str, 
        limit: int = 10,
        threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Search for relevant memories for a user based on query.
        
        Args:
            query: The search query to find relevant memories
            user_id: The user ID to search memories for
            limit: Maximum number of memories to return
            threshold: Minimum relevance threshold (0-1)
            
        Returns:
            List of relevant memory dictionaries
        """
        if not self._memory_client:
            logger.warning("Memory client not initialized, returning empty memories")
            return []
            
        try:
            logger.debug(f"Searching memories for user {user_id} with query: {query[:100]}...")
            
            # Search memories using mem0
            search_results = self._memory_client.search(
                query=query,
                user_id=user_id,
                limit=limit
            )
            
            # Filter by threshold if provided
            relevant_memories = []
            for result in search_results:
                # mem0 returns results with score, filter by threshold
                if result.get("score", 0) >= threshold:
                    relevant_memories.append({
                        "content": result.get("memory", ""),
                        "score": result.get("score", 0),
                        "metadata": result.get("metadata", {}),
                        "id": result.get("id", "")
                    })
            
            logger.info(f"Found {len(relevant_memories)} relevant memories for user {user_id}")
            return relevant_memories
            
        except Exception as e:
            logger.error(f"Error searching memories for user {user_id}: {str(e)}")
            return []

    async def add_memory(
        self, 
        content: str, 
        user_id: str, 
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Add a new memory for a user.
        
        Args:
            content: The memory content to store
            user_id: The user ID to associate the memory with
            metadata: Optional metadata to store with the memory
            
        Returns:
            True if memory was added successfully, False otherwise
        """
        if not self._memory_client:
            logger.warning("Memory client not initialized, cannot add memory")
            return False
            
        try:
            logger.debug(f"Adding memory for user {user_id}: {content[:100]}...")
            
            # Add memory using mem0
            result = self._memory_client.add(
                messages=[{"role": "user", "content": content}],
                user_id=user_id,
                metadata=metadata or {}
            )
            
            logger.info(f"Successfully added memory for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding memory for user {user_id}: {str(e)}")
            return False

    async def add_conversation_memory(
        self, 
        user_message: str, 
        assistant_response: str, 
        user_id: str,
        thread_id: Optional[str] = None,
        model_name: Optional[str] = None
    ) -> bool:
        """
        Add memory from a conversation exchange.
        
        Args:
            user_message: The user's message
            assistant_response: The assistant's response
            user_id: The user ID
            thread_id: Optional thread ID for context
            model_name: Optional model name used
            
        Returns:
            True if memory was added successfully, False otherwise
        """
        if not self._memory_client:
            logger.warning("Memory client not initialized, cannot add conversation memory")
            return False
            
        try:
            # Create conversation context for memory extraction
            conversation = [
                {"role": "user", "content": user_message},
                {"role": "assistant", "content": assistant_response}
            ]
            
            metadata = {}
            if thread_id:
                metadata["thread_id"] = thread_id
            if model_name:
                metadata["model_name"] = model_name
            
            logger.debug(f"Adding conversation memory for user {user_id}")
            
            # Let mem0 extract and store relevant memories from the conversation
            result = self._memory_client.add(
                messages=conversation,
                user_id=user_id,
                metadata=metadata
            )
            
            logger.info(f"Successfully added conversation memory for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding conversation memory for user {user_id}: {str(e)}")
            return False

    async def get_user_memories(
        self, 
        user_id: str, 
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get all memories for a user.
        
        Args:
            user_id: The user ID to get memories for
            limit: Maximum number of memories to return
            
        Returns:
            List of memory dictionaries
        """
        if not self._memory_client:
            logger.warning("Memory client not initialized, returning empty memories")
            return []
            
        try:
            logger.debug(f"Getting all memories for user {user_id}")
            
            # Get all memories for user
            memories = self._memory_client.get_all(user_id=user_id, limit=limit)
            
            logger.info(f"Retrieved {len(memories)} memories for user {user_id}")
            return memories
            
        except Exception as e:
            logger.error(f"Error getting memories for user {user_id}: {str(e)}")
            return []

    async def delete_memory(self, memory_id: str, user_id: str) -> bool:
        """
        Delete a specific memory.
        
        Args:
            memory_id: The ID of the memory to delete
            user_id: The user ID (for verification)
            
        Returns:
            True if memory was deleted successfully, False otherwise
        """
        if not self._memory_client:
            logger.warning("Memory client not initialized, cannot delete memory")
            return False
            
        try:
            logger.debug(f"Deleting memory {memory_id} for user {user_id}")
            
            # Delete memory using mem0
            self._memory_client.delete(memory_id=memory_id)
            
            logger.info(f"Successfully deleted memory {memory_id} for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting memory {memory_id} for user {user_id}: {str(e)}")
            return False


# Global memory service instance
memory_service = MemoryService()
