from typing import cast
from uuid import UUI<PERSON>, uuid4

from agent.models import AgentRunF<PERSON>back, AgentRunFeedbackUpsertRequest
from ninja_common.logging import logger
from ninja_feedback_client.models import (
    ApiFeedbackV2,
    FeedbackUpsertRequest,
    PublicCreateFeedbackV2,
    PublicCreateFeedbackV2Response,
    SuperNinjaAgentRunFeedback,
)
from services.service_clients import get_feedback_service_client
from supabase import AsyncClient


async def _get_feedaback_on_agent_run_row(
    db_client: AsyncClient,
    agent_run_id: UUID,
) -> AgentRunFeedback | None:
    query_result = (
        await db_client.table("agent_runs").select("feedback").eq("id", agent_run_id).execute()
    )

    if not query_result.data:
        raise Exception(f"Agent run DB row with ID {agent_run_id} not found")

    feedback_data = query_result.data[0].get("feedback")
    if not feedback_data:
        return None

    return AgentRunFeedback.model_validate_json(feedback_data)


async def _update_feedback_on_agent_run_row(
    db_client: Async<PERSON>lient,
    agent_run_id: UUID,
    feedback: AgentRunFeedback,
) -> None:
    update_data = {
        "feedback": feedback.model_dump_json(),
    }
    update_result = (
        await db_client.table("agent_runs").update(update_data).eq("id", agent_run_id).execute()
    )

    if not update_result.data:
        raise Exception(f"Failed to add feedback to agent run DB row with ID {agent_run_id}")


def _generate_new_feedback_id() -> UUID:
    return uuid4()


async def submit_agent_run_feedback(
    db_client: AsyncClient,
    user_id: UUID,
    agent_run_id: UUID,
    request: AgentRunFeedbackUpsertRequest,
) -> AgentRunFeedback:
    existing_feedback = await _get_feedaback_on_agent_run_row(db_client, agent_run_id)

    if not existing_feedback:
        feedback_id = _generate_new_feedback_id()
    else:
        feedback_id = existing_feedback.feedback_id

    new_feedback = AgentRunFeedback(
        feedback_id=feedback_id,
        data=request.data,
    )

    await get_feedback_service_client().upsert_feedback(
        user_id,
        feedback_id,
        FeedbackUpsertRequest(data=new_feedback.data),
    )

    await _update_feedback_on_agent_run_row(db_client, agent_run_id, new_feedback)

    logger.info(
        f"Feedback saved for agent run {agent_run_id}",
        extra={
            "feedback_id": feedback_id,
            "agent_run_id": agent_run_id,
        },
    )

    return new_feedback
