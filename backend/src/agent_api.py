#!/usr/bin/env python3
"""
FastAPI wrapper for the agent query functionality from example_agent_query.py.
This allows easy testing with other projects by providing a REST API.
"""

from agent.wrapper import query_agent
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# Create FastAPI app
app = FastAPI(
    title="Agent API",
    description="API for querying the agent (based on example_agent_query.py)",
    version="0.1.0",
)

# Add CORS middleware to allow cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)


# Define request and response models
class AgentQueryRequest(BaseModel):
    query: str = Field(..., description="The query to send to the agent")
    model: str = Field("anthropic/claude-3-7-sonnet-latest", description="The model to use")
    enable_context_manager: bool = Field(False, description="Enable context manager")


class AgentQueryResponse(BaseModel):
    response: str = Field(..., description="The response from the agent")


@app.post("/query", response_model=AgentQueryResponse)
def query(request: AgentQueryRequest):
    """
    Send a query to the agent and get the response.
    This endpoint mimics the functionality of example_agent_query.py.
    """
    try:
        # Call the query_agent function with the same parameters as example_agent_query.py
        response = query_agent(
            query=request.query,
            model_name=request.model,
            enable_context_manager=request.enable_context_manager,
            stream=False,  # Set to False to get the complete response at once
        )

        print(response)
        return {"response": response}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error querying agent: {str(e)}")


# Run the FastAPI app with uvicorn when executed directly
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=7860)
