#!/usr/bin/env python3
"""
Example script demonstrating how to use the agent wrapper to run a query.
"""

import argparse
import sys

from agent.wrapper import query_agent


def main():
    """Run a query with the agent based on command line arguments."""
    # Set up argument parser
    parser = argparse.ArgumentParser(description="Run a query with the agent.")
    parser.add_argument(
        "query",
        nargs="?",
        default="write a python script for hello world",
        help="The query to send to the agent",
    )
    parser.add_argument(
        "--model",
        default="anthropic/claude-3-7-sonnet-latest",
        help="The model to use (default: anthropic/claude-3-7-sonnet-latest)",
    )
    parser.add_argument("--context-manager", action="store_true", help="Enable context manager")

    # Parse arguments
    args = parser.parse_args()

    # Get query from command line arguments or use default
    query = args.query

    print(f"\n🤖 Running agent with query: '{query}'\n")

    try:
        # Run the agent with the query and get the response
        response = query_agent(
            query=query,
            model_name=args.model,
            enable_context_manager=args.context_manager,
            stream=False,  # Set to False to get the complete response at once
        )

        print("\n🤖 Agent Response:\n")
        print(response)

        print("\n✅ Example completed.")
        return 0
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback

        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
