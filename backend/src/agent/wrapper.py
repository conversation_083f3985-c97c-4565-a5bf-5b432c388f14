import asyncio
import j<PERSON>
from typing import Any, Dict, Optional, Union
from uuid import uuid4

from agent.run import run_agent
from agentpress.thread_manager import Thread<PERSON>anager
from dotenv import load_dotenv
from sandbox.sandbox import create_sandbox, get_or_start_sandbox
from services.supabase import DBConnection
from utils.logger import logger


async def run_agent_with_query(
    query: str,
    project_id: Optional[str] = None,
    account_id: Optional[str] = None,
    model_name: str = "anthropic/claude-3-7-sonnet-latest",
    enable_thinking: bool = False,
    reasoning_effort: str = "low",
    enable_context_manager: bool = True,
    stream: bool = False,
) -> str:
    """
    Run the agent with a query and return the final response.

    Args:
        query: The user query to process
        project_id: Optional project ID to use (will create a test project if not provided)
        account_id: Optional account ID to use (will try to get personal account if not provided)
        model_name: The LLM model to use
        enable_thinking: Whether to enable thinking mode
        reasoning_effort: Level of reasoning effort ('low', 'medium', 'high')
        enable_context_manager: Whether to enable context manager
        stream: Whether to stream the response (if False, will collect and return final response)

    Returns:
        The final response from the agent as a string
    """
    # Initialize ThreadManager
    thread_manager = ThreadManager()

    # Create a database connection
    db_connection = DBConnection()
    client = await db_connection.client

    try:
        # If account_id is not provided, try to get user's personal account
        if not account_id:
            account_result = await client.rpc("get_personal_account").execute()
            if account_result.data and len(account_result.data) > 0:
                account_id = account_result.data[0].get("id")

            # If still no account_id, use a default test account
            if not account_id:
                # You should replace this with your actual account ID from Supabase
                account_id = "f6aa2d32-452c-4ad8-b152-c7a6b9581cd4"

        if not account_id:
            raise ValueError("Could not get account ID")

        # If project_id is not provided, find or create a test project
        if not project_id:
            # Find or create a test project in the user's account
            test_project_name = f"test_agent_wrapper_{uuid4().hex[:8]}"
            project_result = (
                await client.table("projects")
                .select("*")
                .eq("name", test_project_name)
                .eq("account_id", account_id)
                .execute()
            )

            if project_result.data and len(project_result.data) > 0:
                # Use existing test project
                project_id = project_result.data[0]["project_id"]
                logger.info(f"Using existing test project: {project_id}")
            else:
                # Create new test project
                project_result = (
                    await client.table("projects")
                    .insert({"name": test_project_name, "account_id": account_id})
                    .execute()
                )
                project_id = project_result.data[0]["project_id"]
                logger.debug(f"Created new test project: {project_id}")

            # Create a sandbox for the project if it doesn't exist
            project_details = (
                await client.table("projects").select("*").eq("project_id", project_id).execute()
            )
            if project_details.data and len(project_details.data) > 0:
                project_data = project_details.data[0]
                sandbox_info = project_data.get("sandbox", {})

                if not sandbox_info or not sandbox_info.get("id"):
                    # Create a sandbox and update the project
                    sandbox_pass = str(uuid4())
                    sandbox = await create_sandbox(sandbox_pass)

                    # Update the project with sandbox info
                    await client.table("projects").update(
                        {"sandbox": {"id": sandbox.id, "password": sandbox_pass}}
                    ).eq("project_id", project_id).execute()

                    logger.debug(f"Created and linked sandbox {sandbox.id} to project {project_id}")

        # Create a thread for this project
        thread_result = (
            await client.table("threads")
            .insert({"project_id": project_id, "account_id": account_id})
            .execute()
        )
        thread_data = thread_result.data[0] if thread_result.data else None

        if not thread_data:
            raise ValueError("No thread data returned")

        thread_id = thread_data["thread_id"]
        logger.debug(f"Agent Thread Created: {thread_id}")

        # Add the user message to the thread
        await thread_manager.add_message(
            thread_id=thread_id,
            type="user",
            content={"role": "user", "content": query},
            is_llm_message=True,
        )

        # Process the agent response
        final_response = await process_agent_query(
            thread_id=thread_id,
            project_id=project_id,
            thread_manager=thread_manager,
            stream=stream,
            model_name=model_name,
            enable_thinking=enable_thinking,
            reasoning_effort=reasoning_effort,
            enable_context_manager=enable_context_manager,
        )

        return final_response

    except Exception as e:
        print(f"Error in run_agent_with_query: {str(e)}")
        import traceback

        traceback.print_exc()
        return f"Error: {str(e)}"


async def process_agent_query(
    thread_id: str,
    project_id: str,
    thread_manager: ThreadManager,
    stream: bool = False,
    model_name: str = "anthropic/claude-3-7-sonnet-latest",
    enable_thinking: Optional[bool] = False,
    reasoning_effort: Optional[str] = "low",
    enable_context_manager: bool = True,
) -> str:
    """
    Process the agent response and return the final result.

    Args:
        thread_id: The thread ID to process
        project_id: The project ID
        thread_manager: The ThreadManager instance
        stream: Whether to stream the response
        model_name: The LLM model to use
        enable_thinking: Whether to enable thinking mode
        reasoning_effort: Level of reasoning effort
        enable_context_manager: Whether to enable context manager

    Returns:
        The final response from the agent as a string
    """
    current_response = ""

    try:
        agent_response = run_agent(
            thread_id=thread_id,
            project_id=project_id,
            stream=stream,
            thread_manager=thread_manager,
            native_max_auto_continues=25,
            model_name=model_name,
            enable_thinking=enable_thinking,
            reasoning_effort=reasoning_effort,
            enable_context_manager=enable_context_manager,
        )

        async for chunk in agent_response:
            if chunk.get("type") == "assistant":
                # Try parsing the content JSON
                try:
                    # Handle content as string or object
                    content = chunk.get("content", "{}")
                    if isinstance(content, str):
                        content_json = json.loads(content)
                    else:
                        content_json = content

                    actual_content = content_json.get("content", "")
                    # Accumulate only text part
                    if actual_content:
                        current_response += actual_content
                except json.JSONDecodeError:
                    # If content is not JSON (e.g., just a string chunk), add directly
                    raw_content = chunk.get("content", "")
                    current_response += raw_content
                except Exception as e:
                    print(f"Error processing assistant chunk: {e}")

        return current_response
    except Exception as e:
        print(f"Error in process_agent_query: {str(e)}")
        import traceback

        traceback.print_exc()
        return f"Error: {str(e)}"


def query_agent(query: str, **kwargs) -> str:
    """
    Synchronous wrapper to run the agent with a query and return the final response.

    Args:
        query: The user query to process
        **kwargs: Additional arguments to pass to run_agent_with_query

    Returns:
        The final response from the agent as a string
    """
    # Load environment variables
    load_dotenv()

    # Get the current event loop or create a new one if needed
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        # If no event loop exists in current thread, create a new one
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

    # Run the async function but don't close the loop
    return loop.run_until_complete(run_agent_with_query(query, **kwargs))
