version: '3.8'

services:
  api:
    container_name: ninja-suna-manus-backend
    build:
      context: .
      dockerfile: ../docker/backend.dockerfile
      args:
        - NINJA_CODE_ARTIFACT_TOKEN=${NINJA_CODE_ARTIFACT_TOKEN}
    ports:
      - "8000:80"
    env_file:
      - .env
    volumes:
      - .:/app
      - ./logs:/app/logs
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - app-network
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - LOG_LEVEL=INFO
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '8'
          memory: 48G
        reservations:
          cpus: '8'
          memory: 32G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    ports:
      - "127.0.0.1:6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - app-network
    command: redis-server --appendonly yes --bind 0.0.0.0 --protected-mode no --maxmemory 8gb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 12G
        reservations:
          cpus: '1'
          memory: 8G

networks:
  app-network:
    driver: bridge

volumes:
  redis_data:
